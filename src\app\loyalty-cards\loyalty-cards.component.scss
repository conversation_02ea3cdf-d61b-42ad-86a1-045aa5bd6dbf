.loyalty-cards-container {
  padding: 20px;
  max-width: 600px;
  margin: 0 auto;
  display: flex;
  flex-direction: column;
  min-height: calc(100vh - 60px);
  position: relative;
  padding-bottom: 80px; // Space for the add button

  // Dark mode styles
  :host-context([data-theme="dark"]) & {
    background-color: var(--primary-bg, #1a1a1a);
  }
}

.header {
  margin-bottom: 20px;

  h1 {
    font-size: 24px;
    font-weight: 600;
    color: #333;

    // Dark mode styles
    :host-context([data-theme="dark"]) & {
      color: white;
    }
  }
}

.content {
  flex: 1;
}

// Empty state styling
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 20px;
  flex: 1;
  min-height: 50vh;

  .empty-illustration {
    position: relative;
    width: 100%;
    max-width: 320px;
    margin-bottom: 0;

    .background-circles {
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      width: 300px;
      height: 300px;
      pointer-events: none;

      .circle {
        position: absolute;
        border-radius: 50%;
        opacity: 0.1;
        animation: float 6s ease-in-out infinite;

        &.circle-1 {
          width: 80px;
          height: 80px;
          background: linear-gradient(135deg, #6B48FF, #8B5FFF);
          top: 20px;
          left: 40px;
          animation-delay: 0s;
        }

        &.circle-2 {
          width: 60px;
          height: 60px;
          background: linear-gradient(135deg, #FF6B6B, #FF8E8E);
          top: 180px;
          right: 30px;
          animation-delay: 2s;
        }

        &.circle-3 {
          width: 40px;
          height: 40px;
          background: linear-gradient(135deg, #4ECDC4, #7FDBFF);
          bottom: 60px;
          left: 60px;
          animation-delay: 4s;
        }
      }
    }

    .main-content {
      position: relative;
      z-index: 2;
      text-align: center;

      .sad-face {
        position: relative;
        margin-bottom: 32px;
        display: inline-block;

        .face {
          width: 160px;
          height: 160px;
          background: linear-gradient(145deg, #f8f9fa, #e9ecef);
          border-radius: 50%;
          position: relative;
          display: flex;
          align-items: center;
          justify-content: center;
          box-shadow: 
            0 8px 32px rgba(0, 0, 0, 0.08),
            inset 0 2px 8px rgba(255, 255, 255, 0.9),
            inset 0 -2px 8px rgba(0, 0, 0, 0.05);
          transition: all 0.3s ease;

          // Dark mode styles
          :host-context([data-theme="dark"]) & {
            background: linear-gradient(145deg, #404040, #2d2d2d);
            box-shadow: 
              0 8px 32px rgba(0, 0, 0, 0.3),
              inset 0 2px 8px rgba(255, 255, 255, 0.1),
              inset 0 -2px 8px rgba(0, 0, 0, 0.3);
          }

          &:hover {
            transform: translateY(-2px);
            box-shadow: 
              0 12px 40px rgba(0, 0, 0, 0.12),
              inset 0 2px 8px rgba(255, 255, 255, 0.9),
              inset 0 -2px 8px rgba(0, 0, 0, 0.05);

            // Dark mode styles
            :host-context([data-theme="dark"]) & {
              box-shadow: 
                0 12px 40px rgba(0, 0, 0, 0.4),
                inset 0 2px 8px rgba(255, 255, 255, 0.1),
                inset 0 -2px 8px rgba(0, 0, 0, 0.3);
            }
          }

          .eyes {
            position: absolute;
            top: 50px;
            width: 100%;
            display: flex;
            justify-content: space-between;
            padding: 0 45px;

            .eye {
              width: 20px;
              height: 20px;
              background: #ffffff;
              border-radius: 50%;
              position: relative;
              box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.1);
              animation: blink 4s infinite;

              // Dark mode styles
              :host-context([data-theme="dark"]) & {
                background: #555;
              }

              .pupil {
                width: 8px;
                height: 8px;
                background: #333;
                border-radius: 50%;
                position: absolute;
                top: 50%;
                left: 50%;
                transform: translate(-50%, -50%);
                animation: look 3s ease-in-out infinite;

                // Dark mode styles
                :host-context([data-theme="dark"]) & {
                  background: #888;
                }
              }
            }
          }

          .mouth {
            position: absolute;
            bottom: 40px;
            left: 50%;
            transform: translateX(-50%);
            width: 60px;
            height: 30px;
            color: #999;

            // Dark mode styles
            :host-context([data-theme="dark"]) & {
              color: #666;
            }

            .mouth-svg {
              width: 100%;
              height: 100%;
            }
          }
        }

        .tears {
          position: absolute;
          top: 120px;
          width: 100%;

          .tear {
            position: absolute;
            width: 8px;
            height: 24px;
            background: linear-gradient(180deg, #87CEEB, #4682B4);
            border-radius: 50% 50% 50% 50% / 60% 60% 40% 40%;
            opacity: 0;
            animation: tearDrop 3s ease-in-out infinite;

            &.tear-1 {
              left: 35px;
              animation-delay: 0.5s;
            }

            &.tear-2 {
              right: 35px;
              animation-delay: 1.5s;
            }
          }
        }
      }

      .empty-content {
        .empty-title {
          font-size: 24px;
          font-weight: 700;
          color: #2d3748;
          margin: 0 0 12px 0;
          letter-spacing: -0.5px;

          // Dark mode styles
          :host-context([data-theme="dark"]) & {
            color: #f7fafc;
          }
        }

        .empty-subtitle {
          font-size: 16px;
          color: #718096;
          margin: 0;
          line-height: 1.5;
          max-width: 280px;
          margin: 0 auto;

          // Dark mode styles
          :host-context([data-theme="dark"]) & {
            color: #a0aec0;
          }
        }
      }
    }
  }
}

// Keyframe animations
@keyframes float {
  0%, 100% {
    transform: translateY(0px) rotate(0deg);
  }
  50% {
    transform: translateY(-20px) rotate(10deg);
  }
}

@keyframes blink {
  0%, 90%, 100% {
    transform: scaleY(1);
  }
  95% {
    transform: scaleY(0.1);
  }
}

@keyframes look {
  0%, 50%, 100% {
    transform: translate(-50%, -50%);
  }
  25% {
    transform: translate(-70%, -50%);
  }
  75% {
    transform: translate(-30%, -50%);
  }
}

@keyframes tearDrop {
  0% {
    opacity: 0;
    transform: translateY(-10px) scale(0.8);
  }
  20% {
    opacity: 1;
    transform: translateY(0px) scale(1);
  }
  80% {
    opacity: 1;
    transform: translateY(15px) scale(1);
  }
  100% {
    opacity: 0;
    transform: translateY(25px) scale(0.8);
  }
}

// Cards list styling
.cards-list {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.card-item {
  background: var(--card-bg, white);
  border-radius: 12px;
  overflow: hidden;
  box-shadow:
    0 15px 40px rgba(107, 72, 255, 0.25),
    0 8px 20px rgba(168, 85, 247, 0.2),
    0 0 0 1px rgba(171, 85, 247, 0.1),
    inset 0 0 0 1px rgba(255, 255, 255, 0.4);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  cursor: pointer;
  position: relative;

  &:hover {
    transform: translateY(-5px);
    box-shadow:
      0 20px 50px rgba(107, 72, 255, 0.35),
      0 12px 30px rgba(168, 85, 247, 0.25),
      0 0 0 1px rgba(171, 85, 247, 0.15),
      inset 0 0 0 1px rgba(255, 255, 255, 0.5);
  }

  // Dark mode styles
  :host-context([data-theme="dark"]) & {
    box-shadow:
      0 15px 40px rgba(0, 0, 0, 0.4),
      0 8px 20px rgba(107, 72, 255, 0.25),
      0 0 0 1px rgba(171, 85, 247, 0.15),
      inset 0 0 0 1px rgba(255, 255, 255, 0.1);

    &:hover {
      box-shadow:
        0 20px 50px rgba(0, 0, 0, 0.5),
        0 12px 30px rgba(107, 72, 255, 0.3),
        0 0 0 1px rgba(171, 85, 247, 0.2),
        inset 0 0 0 1px rgba(255, 255, 255, 0.15);
    }
  }

  .card-header {
    padding: 15px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    box-shadow: 0 6px 15px rgba(0, 0, 0, 0.15);
    position: relative;
    z-index: 1;

    .card-name {
      font-size: 18px;
      font-weight: 600;
      color: white;
    }

    .card-options-wrapper {
      position: relative;

      .card-options {
        background: none;
        border: none;
        color: white;
        cursor: pointer;
        font-size: 16px;
        opacity: 0.8;
        padding: 5px;
        border-radius: 50%;

        &:hover {
          opacity: 1;
          background-color: rgba(255, 255, 255, 0.1);
        }
      }

      .card-options-menu {
        position: absolute;
        top: 100%;
        right: 0;
        background-color: white;
        border-radius: 8px;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        z-index: 10;
        min-width: 150px;
        overflow: hidden;

        // Dark mode styles
        :host-context([data-theme="dark"]) & {
          background-color: #333;
          box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
        }

        .option-item {
          display: flex;
          align-items: center;
          gap: 8px;
          padding: 10px 15px;
          width: 100%;
          text-align: left;
          border: none;
          background: none;
          cursor: pointer;
          color: #333;
          font-size: 14px;

          // Dark mode styles
          :host-context([data-theme="dark"]) & {
            color: #fff;
          }

          &:hover {
            background-color: #f5f5f5;

            // Dark mode styles
            :host-context([data-theme="dark"]) & {
              background-color: #444;
            }
          }

          &.delete-card {
            color: #e53935;

            &:hover {
              background-color: rgba(229, 57, 53, 0.1);
            }
          }
        }
      }
    }
  }

  .card-body {
    padding: 20px;
    background-color: white !important;
    box-shadow:
      0 6px 15px rgba(168, 85, 247, 0.25),
      0 3px 8px rgba(171, 85, 247, 0.2),
      inset 0 0 0 1px rgba(171, 85, 247, 0.1);

    // Dark mode styles
    :host-context([data-theme="dark"]) & {
      box-shadow:
        0 6px 15px rgba(168, 85, 247, 0.3),
        0 3px 8px rgba(171, 85, 247, 0.25),
        inset 0 0 0 1px rgba(171, 85, 247, 0.15);
    }

    .barcode {
      display: flex;
      flex-direction: column;
      align-items: center;
      gap: 10px;

      .barcode-image {
        width: 100%;
        height: 80px;
        display: flex;
        align-items: center;
        justify-content: center;

        .barcode-line {
          height: 80px;
          background-color: black;
          display: inline-block;
        }

        .uploaded-barcode {
          max-width: 100%;
          max-height: 80px;
          object-fit: contain;
        }
      }

      .card-number {
        font-size: 16px;
        color: #6B48FF;
        font-weight: 500;
        letter-spacing: 1px;

        // Dark mode styles
        :host-context([data-theme="dark"]) & {
          color: #a855f7;
        }
      }
    }
  }
}

// Add Card Button
.add-card-btn {
  position: fixed;
  bottom: 20px;
  left: 50%;
  transform: translateX(-50%);
  width: 90%;
  max-width: 400px;
  padding: 16px;
  background-color: #6B48FF;
  color: white;
  border: none;
  border-radius: 8px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 4px 12px rgba(107, 72, 255, 0.3);

  &:hover {
    background-color: #5a3dd9;
    transform: translateX(-50%) translateY(-2px);
    box-shadow: 0 6px 16px rgba(107, 72, 255, 0.4);
  }

  // Dark mode styles
  :host-context([data-theme="dark"]) & {
    box-shadow: 0 4px 12px rgba(107, 72, 255, 0.5);

    &:hover {
      box-shadow: 0 6px 16px rgba(107, 72, 255, 0.6);
    }
  }
}

// Responsive adjustments
@media (max-width: 768px) {
  .loyalty-cards-container {
    padding: 15px;
    padding-bottom: 80px;
  }

  .header h1 {
    font-size: 20px;
  }

  .empty-state {
    padding: 30px 15px;
    min-height: 45vh;

    .empty-illustration {
      max-width: 280px;

      .background-circles {
        width: 260px;
        height: 260px;

        .circle {
          &.circle-1 {
            width: 60px;
            height: 60px;
            top: 15px;
            left: 30px;
          }

          &.circle-2 {
            width: 45px;
            height: 45px;
            top: 160px;
            right: 25px;
          }

          &.circle-3 {
            width: 30px;
            height: 30px;
            bottom: 50px;
            left: 50px;
          }
        }
      }

      .main-content {
        .sad-face {
          margin-bottom: 28px;

          .face {
            width: 140px;
            height: 140px;

            .eyes {
              top: 44px;
              padding: 0 40px;

              .eye {
                width: 18px;
                height: 18px;

                .pupil {
                  width: 7px;
                  height: 7px;
                }
              }
            }

            .mouth {
              bottom: 35px;
              width: 55px;
              height: 28px;
            }
          }

          .tears {
            top: 105px;

            .tear {
              width: 7px;
              height: 20px;

              &.tear-1 {
                left: 30px;
              }

              &.tear-2 {
                right: 30px;
              }
            }
          }
        }

        .empty-content {
          .empty-title {
            font-size: 22px;
            margin-bottom: 10px;
          }

          .empty-subtitle {
            font-size: 15px;
            max-width: 260px;
          }
        }
      }
    }
  }

  .add-card-btn {
    width: 95%;
    padding: 14px;
  }

  .card-options-menu {
    right: -10px;
  }
}

@media (max-width: 480px) {
  .empty-state {
    padding: 25px 15px;
    min-height: 40vh;

    .empty-illustration {
      max-width: 240px;

      .background-circles {
        width: 220px;
        height: 220px;

        .circle {
          &.circle-1 {
            width: 50px;
            height: 50px;
            top: 12px;
            left: 25px;
          }

          &.circle-2 {
            width: 35px;
            height: 35px;
            top: 140px;
            right: 20px;
          }

          &.circle-3 {
            width: 25px;
            height: 25px;
            bottom: 40px;
            left: 40px;
          }
        }
      }

      .main-content {
        .sad-face {
          margin-bottom: 24px;

          .face {
            width: 120px;
            height: 120px;

            .eyes {
              top: 38px;
              padding: 0 35px;

              .eye {
                width: 16px;
                height: 16px;

                .pupil {
                  width: 6px;
                  height: 6px;
                }
              }
            }

            .mouth {
              bottom: 30px;
              width: 45px;
              height: 22px;
            }
          }

          .tears {
            top: 90px;

            .tear {
              width: 6px;
              height: 18px;

              &.tear-1 {
                left: 25px;
              }

              &.tear-2 {
                right: 25px;
              }
            }
          }
        }

        .empty-content {
          .empty-title {
            font-size: 20px;
            margin-bottom: 8px;
          }

          .empty-subtitle {
            font-size: 14px;
            max-width: 220px;
            line-height: 1.4;
          }
        }
      }
    }
  }
}