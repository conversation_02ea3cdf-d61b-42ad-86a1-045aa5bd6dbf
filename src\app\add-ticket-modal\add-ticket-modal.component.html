<div class="modal-overlay" (click)="close.emit()">
  <div class="modal-content" (click)="$event.stopPropagation()">
    <div class="modal-header">
      <h2>Add New Ticket</h2>
      <button class="close-btn" (click)="close.emit()">
        <i class="fas fa-times"></i>
      </button>
    </div>

    <div class="input-mode-selector">
      <div class="mode-tabs">
        <button
          class="mode-tab"
          [class.active]="inputMode === 'manual'"
          (click)="inputMode = 'manual'">
          <i class="fas fa-keyboard"></i>
          Manual Input
        </button>
        <button
          class="mode-tab"
          [class.active]="inputMode === 'qrcode'"
          (click)="inputMode = 'qrcode'">
          <i class="fas fa-qrcode"></i>
          QR Code
        </button>
      </div>
    </div>

    <div class="modal-body">
      <!-- QR CODE MODE -->
      <div *ngIf="inputMode === 'qrcode'" class="qr-mode-container">
        <div class="qr-upload-section">
          <p class="mode-description">Upload a QR code image to automatically fill in ticket details</p>
          <div class="qr-upload-container">
            <input type="file"
                   id="qrCodeUpload"
                   accept="image/*"
                   (change)="onQrCodeUpload($event)"
                   #qrFileInput>
            <button class="upload-btn" (click)="qrFileInput.click()">
              <i class="fas fa-qrcode"></i>
              Select QR Code
            </button>
            <div class="qr-preview" *ngIf="qrCodePreview">
              <img [src]="qrCodePreview" alt="QR Code Preview">
              <button class="remove-qr-btn" (click)="removeQrCode()">
                <i class="fas fa-times"></i>
              </button>
            </div>
          </div>
        </div>
      </div>

      <!-- MANUAL INPUT MODE -->
      <div *ngIf="inputMode === 'manual'" class="manual-mode-container">
        <div class="form-group">
          <label for="ticketId">Ticket ID</label>
          <input type="text"
                 id="ticketId"
                 [(ngModel)]="ticketId"
                 placeholder="Enter ticket ID"
                 [ngClass]="{'has-prefix': ticketId.startsWith('#')}"
                 (input)="ticketId = ticketId.startsWith('#') ? ticketId : '#' + ticketId">
        </div>

        <div class="form-group">
          <label for="brandName">Brand Name</label>
          <div class="autocomplete-container">
            <input type="text"
                  id="brandName"
                  [(ngModel)]="brandName"
                  (input)="onBrandNameInput()"
                  (focus)="showBrandSuggestions = true"
                  (blur)="onBrandInputBlur()"
                  placeholder="Select or enter brand name"
                  autocomplete="off">
            <div class="dropdown-icon">
              <i class="fas fa-angle-down"></i>
            </div>
            <div class="autocomplete-dropdown" *ngIf="showBrandSuggestions && filteredBrands.length > 0">
              <div class="autocomplete-item"
                  *ngFor="let brand of filteredBrands"
                  (mousedown)="selectBrand(brand)"
                  [class.active]="brand.toLowerCase() === brandName.toLowerCase()">
                {{ brand }}
              </div>
            </div>
          </div>
        </div>

        <div *ngIf="isCollaboratingBrand" class="products-section">
          <h3>Products</h3>
          <div class="add-product-form">
            <input type="text"
                   [(ngModel)]="newProductName"
                   placeholder="Product name"
                   (keyup.enter)="addProduct()">
            <input type="number"
                   [(ngModel)]="newProductPrice"
                   placeholder="Price"
                   (keyup.enter)="addProduct()">
            <button (click)="addProduct()">Add Product</button>
          </div>

          <div class="products-list">
            <div class="product-item" *ngFor="let product of products; let i = index">
              <div class="product-info" *ngIf="editingProductIndex !== i">
                <span class="product-name">{{product.name}}</span>
                <span class="product-price">{{product.price}} TND</span>
              </div>
              <div class="product-edit-form" *ngIf="editingProductIndex === i">
                <input type="text" [(ngModel)]="tempProduct.name" placeholder="Product name" class="edit-input">
                <input type="number" [(ngModel)]="tempProduct.price" placeholder="Price" class="edit-input">
                <div class="edit-actions">
                  <button class="save-edit-btn" (click)="saveProductEdit()">Save</button>
                  <button class="cancel-edit-btn" (click)="cancelEdit()">Cancel</button>
                </div>
              </div>
              <div class="product-actions" *ngIf="editingProductIndex !== i">
                <button class="edit-btn" (click)="editProduct(i)">
                  <i class="fas fa-edit"></i>
                </button>
                <button class="delete-btn" (click)="removeProduct(i)">
                  <i class="fas fa-trash"></i>
                </button>
              </div>
            </div>
          </div>

          <div class="total-amount" *ngIf="products.length > 0">
            <span>Total Amount:</span>
            <span>{{totalAmount | currency}}</span>
          </div>
        </div>

        <div *ngIf="!isCollaboratingBrand" class="form-group">
          <label for="totalAmount">Total Amount</label>
          <input type="number"
                 id="totalAmount"
                 [(ngModel)]="totalAmount"
                 placeholder="Enter total amount">
        </div>
      </div>
    </div>

    <div class="modal-footer">
      <button class="cancel-btn" (click)="close.emit()">Cancel</button>
      <button class="save-btn"
              (click)="saveTicket()"
              [disabled]="(inputMode === 'manual' && (!ticketId || !brandName || (!isCollaboratingBrand && totalAmount <= 0) || (isCollaboratingBrand && products.length === 0))) ||
                         (inputMode === 'qrcode' && !qrCodePreview)">
        Save
      </button>
    </div>
  </div>
</div>