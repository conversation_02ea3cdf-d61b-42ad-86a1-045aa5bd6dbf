.new-loyalty-card-container {
  padding: 20px;
  max-width: 600px;
  margin: 0 auto;
  display: flex;
  flex-direction: column;
  min-height: calc(100vh - 60px);
  height: 100%;

  // Scrollbar styles for Webkit browsers
  *::-webkit-scrollbar {
    width: 8px;
  }

  *::-webkit-scrollbar-track {
    background: transparent;
  }

  *::-webkit-scrollbar-thumb {
    background-color: rgba(107, 72, 255, 0.3);
    border-radius: 4px;

    &:hover {
      background-color: rgba(107, 72, 255, 0.5);
    }
  }

  // Scrollbar styles for Firefox
  * {
    scrollbar-width: thin;
    scrollbar-color: rgba(107, 72, 255, 0.3) transparent;
  }

  // Dark mode styles
  :host-context([data-theme="dark"]) & {
    background-color: var(--primary-bg, #1a1a1a);
  }

  // Scrollbar styles
  * {
    &::-webkit-scrollbar {
      width: 8px;
    }

    &::-webkit-scrollbar-track {
      background: transparent;
    }

    &::-webkit-scrollbar-thumb {
      background-color: rgba(107, 72, 255, 0.3);
      border-radius: 4px;

      &:hover {
        background-color: rgba(107, 72, 255, 0.5);
      }
    }
  }

  // Firefox scrollbar styles
  * {
    scrollbar-width: thin;
    scrollbar-color: rgba(107, 72, 255, 0.3) transparent;
  }
}

.header {
  margin-bottom: 20px;

  h1 {
    font-size: 24px;
    font-weight: 600;
    color: #333;

    // Dark mode styles
    :host-context([data-theme="dark"]) & {
      color: white;
    }
  }
}

.card-form {
  background: var(--card-bg, white);
  border-radius: 12px;
  padding: 2rem;
  border: 1px solid rgba(171, 85, 247, 0.2);
  box-shadow:
    0 10px 30px rgba(107, 72, 255, 0.15),
    0 6px 15px rgba(168, 85, 247, 0.1),
    0 0 0 1px rgba(171, 85, 247, 0.05),
    inset 0 0 0 1px rgba(255, 255, 255, 0.3);
  animation: modalGlow 1.5s ease-in-out infinite alternate;
  text-align: center;
  position: relative;
  overflow: hidden;

  &::before {
    content: '';
    position: absolute;
    top: -2px;
    left: -2px;
    right: -2px;
    bottom: -2px;
    background: linear-gradient(135deg, rgba(107, 72, 255, 0.4), rgba(168, 85, 247, 0.4), rgba(183, 148, 244, 0.4));
    border-radius: 14px;
    z-index: -1;
    opacity: 0.5;
    filter: blur(8px);
  }

  .form-container {
    max-width: 400px;
    margin: 0 auto;
  }

  .form-icon {
    margin-bottom: 1.5rem;
    color: #6B48FF;
    font-size: 3rem;
    opacity: 0.8;
    transition: transform 0.3s ease;

    &:hover {
      transform: scale(1.05);
    }
  }

  .form-title {
    font-size: 1.5rem;
    font-weight: 600;
    margin-bottom: 1rem;
    color: #2c3e50;
  }

  .form-description {
    color: #666;
    font-size: 1rem;
    line-height: 1.5;
    margin-bottom: 2rem;
    opacity: 0.8;
  }

  // Dark mode styles
  :host-context([data-theme="dark"]) & {
    background-color: #1e1e2d;
    border-color: rgba(171, 85, 247, 0.3);
    box-shadow:
      0 10px 30px rgba(0, 0, 0, 0.3),
      0 6px 15px rgba(107, 72, 255, 0.15),
      0 0 0 1px rgba(171, 85, 247, 0.1),
      inset 0 0 0 1px rgba(255, 255, 255, 0.05);

    .form-title {
      color: #fff;
    }

    .form-description {
      color: #aaa;
    }
  }
}

.form-group {
  margin-bottom: 20px;

  label {
    display: block;
    margin-bottom: 8px;
    font-size: 16px;
    font-weight: 500;
    color: #333;

    // Dark mode styles
    :host-context([data-theme="dark"]) & {
      color: #eee;
    }
  }

  .card-input {
    width: 100%;
    padding: 12px;
    border: 1px solid #ddd;
    border-radius: 8px;
    font-size: 16px;
    transition: border-color 0.3s ease;
    background-color: white;
    color: #333;

    // Always keep white background in dark mode
    :host-context([data-theme="dark"]) & {
      background-color: white;
      color: #333;
      border-color: #aaa;
    }

    &:focus {
      outline: none;
      border-color: #6B48FF;
    }

    &::placeholder {
      color: #aaa;
      font-size: 14px;
    }
  }

  // Barcode upload styles
  .barcode-upload {
    display: flex;
    flex-direction: column;
    gap: 12px;

    .barcode-preview {
      width: 100%;
      height: 120px;
      border: 1px dashed #ddd;
      border-radius: 8px;
      display: flex;
      align-items: center;
      justify-content: center;
      overflow: hidden;

      // Dark mode styles
      :host-context([data-theme="dark"]) & {
        border-color: #555;
      }

      img {
        max-width: 100%;
        max-height: 100%;
        object-fit: contain;
      }
    }

    .upload-btn {
      padding: 12px 16px;
      background: #6B48FF;
      color: white;
      border: none;
      border-radius: 8px;
      box-shadow:
        0 4px 15px rgba(107, 72, 255, 0.25),
        0 2px 4px rgba(168, 85, 247, 0.2),
        inset 0 1px 1px rgba(255, 255, 255, 0.3);
      text-shadow: 0 1px 1px rgba(0, 0, 0, 0.2);
      position: relative;
      overflow: hidden;
      font-size: 14px;
      font-weight: 500;
      color: #555;
      cursor: pointer;
      transition: all 0.3s ease;
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 8px;

      // Dark mode styles
      :host-context([data-theme="dark"]) & {
        background-color: #444;
        border-color: #555;
        color: #eee;
      }

      &::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: linear-gradient(to bottom, rgba(255, 255, 255, 0.2), rgba(255, 255, 255, 0));
        opacity: 0;
        transition: opacity 0.3s ease;
      }

      &:hover {
        transform: translateY(-2px);
        box-shadow:
          0 8px 20px rgba(107, 72, 255, 0.3),
          0 4px 8px rgba(168, 85, 247, 0.25),
          inset 0 1px 1px rgba(255, 255, 255, 0.4);

        &::before {
          opacity: 1;
        }

        i {
          transform: scale(1.1);
          color: #A855F7;
        }
      }

      // Dark mode styles
      :host-context([data-theme="dark"]) & {
        box-shadow:
          0 4px 15px rgba(0, 0, 0, 0.3),
          0 2px 4px rgba(107, 72, 255, 0.3),
          inset 0 1px 1px rgba(255, 255, 255, 0.1);

        &:hover {
          box-shadow:
            0 8px 20px rgba(0, 0, 0, 0.4),
            0 4px 8px rgba(107, 72, 255, 0.35),
            inset 0 1px 1px rgba(255, 255, 255, 0.2);
        }
      }
      i {
        font-size: 16px;
        color: white;
        transition: transform 0.3s ease, color 0.3s ease;
      }
    }
  }

  /* Custom scrollbar styling */
  & {
    scrollbar-width: thin;
    scrollbar-color: rgba(107, 72, 255, 0.3) transparent;

    &::-webkit-scrollbar {
      width: 6px;
      height: 6px;
    }

    &::-webkit-scrollbar-track {
      background: transparent;
      margin: 10px 0;
    }

    &::-webkit-scrollbar-thumb {
      background-color: rgba(107, 72, 255, 0.3);
      border-radius: 3px;

      &:hover {
        background-color: rgba(107, 72, 255, 0.5);
      }
    }
  }

  .card-name-input-wrapper {
    position: relative;
    width: 100%;
  }

  .autocomplete-dropdown {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    background: var(--card-bg, white);
    border: 1px solid rgba(171, 85, 247, 0.2);
    border-radius: 8px;
    margin-top: 4px;
    max-height: 200px;
    overflow-y: auto;
    z-index: 1000;
    box-shadow:
      0 10px 30px rgba(107, 72, 255, 0.15),
      0 6px 15px rgba(168, 85, 247, 0.1),
      0 0 0 1px rgba(171, 85, 247, 0.05),
      inset 0 0 0 1px rgba(255, 255, 255, 0.3);

    // Dark mode styles
    :host-context([data-theme="dark"]) & {
      background: #1e1e2d;
      border-color: rgba(171, 85, 247, 0.3);
      box-shadow:
        0 10px 30px rgba(0, 0, 0, 0.3),
        0 6px 15px rgba(107, 72, 255, 0.15),
        0 0 0 1px rgba(171, 85, 247, 0.1),
        inset 0 0 0 1px rgba(255, 255, 255, 0.05);
    }

    .brand-search-info {
      padding: 12px;
      color: var(--text-color, #666);
      text-align: center;
      font-style: italic;

      // Dark mode styles
      :host-context([data-theme="dark"]) & {
        color: #aaa;
      }
    }

    .brand-option {
      padding: 12px 16px;
      cursor: pointer;
      color: var(--text-color, #333);
      transition: all 0.2s ease;
      border-bottom: 1px solid rgba(0,0,0,0.05);
      text-align: left;

      &:last-child {
        border-bottom: none;
      }

      &:hover {
        background-color: var(--hover-bg, rgba(107, 72, 255, 0.1));
        color: #6B48FF;
        transform: translateX(4px);
      }

      // Dark mode styles
      :host-context([data-theme="dark"]) & {
        color: #eee;
        border-bottom-color: rgba(255, 255, 255, 0.1);

        &:hover {
          background-color: rgba(107, 72, 255, 0.2);
        }
      }
    }
  }
}

.card-form {
  .color-selector {
    display: flex;
    flex-wrap: wrap;
    gap: 12px;

    .color-option {
      width: 40px;
      height: 40px;
      border-radius: 50%;
      cursor: pointer;
      transition: transform 0.2s ease, box-shadow 0.2s ease;

      &:hover {
        transform: scale(1.1);
      }

      &.selected {
        transform: scale(1.15);
        box-shadow: 0 0 0 3px white, 0 0 0 5px #6B48FF;

        // Dark mode styles
        :host-context([data-theme="dark"]) & {
          box-shadow: 0 0 0 3px #2d2d2d, 0 0 0 5px #6B48FF;
        }
      }
    }
  }
}

.add-card-btn {
  width: 100%;
  padding: 16px;
  margin-top: 20px;
  background-color: #6B48FF;
  color: white;
  border: none;
  border-radius: 8px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;

  &:hover:not(.disabled) {
    background-color: #5a3dd9;
    transform: translateY(-2px);
    box-shadow: 0 6px 16px rgba(107, 72, 255, 0.4);
  }

  &.disabled {
    background-color: #ccc;
    cursor: not-allowed;

    // Dark mode styles
    :host-context([data-theme="dark"]) & {
      background-color: #555;
    }
  }

  // Dark mode styles
  :host-context([data-theme="dark"]) & {
    box-shadow: 0 4px 12px rgba(107, 72, 255, 0.3);

    &:hover:not(.disabled) {
      box-shadow: 0 6px 16px rgba(107, 72, 255, 0.5);
    }
  }
}

// Keyframes for modal glow effect
@keyframes modalGlow {
  from {
    box-shadow:
      0 10px 30px rgba(107, 72, 255, 0.15),
      0 6px 15px rgba(168, 85, 247, 0.1),
      0 0 0 1px rgba(171, 85, 247, 0.05),
      inset 0 0 0 1px rgba(255, 255, 255, 0.3);
  }
  to {
    box-shadow:
      0 15px 40px rgba(107, 72, 255, 0.2),
      0 8px 20px rgba(168, 85, 247, 0.15),
      0 0 0 1px rgba(171, 85, 247, 0.1),
      inset 0 0 0 1px rgba(255, 255, 255, 0.4);
  }
}

// Responsive adjustments
@media (max-width: 768px) {
  .new-loyalty-card-container {
    padding: 15px;
  }

  .header h1 {
    font-size: 20px;
  }

  .card-form {
    padding: 20px;
  }

  .form-group label {
    font-size: 14px;
  }

  .form-group .card-input {
    padding: 10px;
    font-size: 14px;
  }

  .barcode-upload {
    .barcode-preview {
      height: 100px;
    }

    .upload-btn {
      padding: 10px;
      font-size: 13px;
    }
  }

  .color-selector {
    .color-option {
      width: 35px;
      height: 35px;
    }
  }

  .add-card-btn {
    padding: 14px;
    font-size: 14px;
  }
}
