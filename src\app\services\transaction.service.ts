import { Injectable } from '@angular/core';
import { BehaviorSubject } from 'rxjs';

@Injectable({
  providedIn: 'root'
})
export class TransactionService {
  // Store the current transaction data
  private transactionSource = new BehaviorSubject<any>(null);
  currentTransaction = this.transactionSource.asObservable();

  constructor() { }

  // Method to update the current transaction
  setCurrentTransaction(transaction: any) {
    this.transactionSource.next(transaction);
  }
}
