import { Injectable } from '@angular/core';
import { BehaviorSubject, Observable } from 'rxjs';
import { LoyaltyCard } from '../models/loyalty-card.model';
import { AppStorageService } from './app-storage.service';

@Injectable({
  providedIn: 'root'
})
export class LoyaltyCardService {
  private readonly LOYALTY_CARDS = 'loyalty_cards';
  private loyaltyCards: LoyaltyCard[] = [];
  private loyaltyCardsSubject = new BehaviorSubject<LoyaltyCard[]>([]);
  // Flag to determine if we're in development mode
  private readonly isDevelopmentMode = true; // Set to true for development mode

  constructor(private appStorage: AppStorageService) {
    // Initialize the storage key in AppStorageService
    this.initializeStorage();
    // Load loyalty cards
    this.loadLoyaltyCards();
  }

  private initializeStorage(): void {
    // Check if the LOYALTY_CARDS key exists in the memoryStorage
    if (!this.appStorage['memoryStorage'].has(this.LOYALTY_CARDS)) {
      // Initialize with empty array
      this.appStorage['memoryStorage'].set(this.LOYALTY_CARDS, []);
    }
  }

  private loadLoyaltyCards(): void {
    try {
      // In development mode, we don't want to load from localStorage to ensure data is cleared on reload
      if (this.isDevelopmentMode) {
        // Only use memory storage in development mode
        this.loyaltyCards = this.appStorage['memoryStorage'].get(this.LOYALTY_CARDS) || [];
      } else {
        // In production mode, try to get loyalty cards from localStorage first for persistence
        const savedCards = localStorage.getItem(this.LOYALTY_CARDS);
        if (savedCards) {
          const parsedCards = JSON.parse(savedCards);
          // Convert string dates back to Date objects
          this.loyaltyCards = parsedCards.map((card: any) => ({
            ...card,
            createdAt: new Date(card.createdAt)
          }));
        } else {
          // If not in localStorage, try to get from AppStorageService
          this.loyaltyCards = this.appStorage['memoryStorage'].get(this.LOYALTY_CARDS) || [];
        }
      }

      // Update the subject
      this.loyaltyCardsSubject.next([...this.loyaltyCards]);
    } catch (e) {
      console.error('Error loading loyalty cards:', e);
      this.loyaltyCards = [];
      this.loyaltyCardsSubject.next([]);
    }
  }

  private saveLoyaltyCards(): void {
    try {
      // Save to AppStorageService
      this.appStorage['memoryStorage'].set(this.LOYALTY_CARDS, this.loyaltyCards);

      // Only save to localStorage in production mode
      if (!this.isDevelopmentMode) {
        localStorage.setItem(this.LOYALTY_CARDS, JSON.stringify(this.loyaltyCards));
      }

      // Update the subject
      this.loyaltyCardsSubject.next([...this.loyaltyCards]);
    } catch (e) {
      console.error('Error saving loyalty cards:', e);
    }
  }

  getLoyaltyCards(): Observable<LoyaltyCard[]> {
    return this.loyaltyCardsSubject.asObservable();
  }

  addLoyaltyCard(card: Omit<LoyaltyCard, 'id' | 'createdAt'>): LoyaltyCard {
    const newCard: LoyaltyCard = {
      ...card,
      id: Date.now().toString(),
      createdAt: new Date()
    };

    this.loyaltyCards.unshift(newCard);
    this.saveLoyaltyCards();

    return newCard;
  }

  deleteLoyaltyCard(id: string): void {
    this.loyaltyCards = this.loyaltyCards.filter(card => card.id !== id);
    this.saveLoyaltyCards();
  }

  getLoyaltyCardById(id: string): LoyaltyCard | undefined {
    return this.loyaltyCards.find(card => card.id === id);
  }

  updateCardBarcode(id: string, barcodeImage: string): void {
    const cardIndex = this.loyaltyCards.findIndex(card => card.id === id);
    if (cardIndex !== -1) {
      this.loyaltyCards[cardIndex] = {
        ...this.loyaltyCards[cardIndex],
        barcodeImage
      };
      this.saveLoyaltyCards();
    }
  }
}
