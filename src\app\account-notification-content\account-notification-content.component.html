<div class="notification-content-container">
  <!-- Notification List View -->
  <div class="notification-list-view" *ngIf="!selectedNotification()">
    <!-- Header -->
    <div class="notification-header">
      <div class="header-title">
        <h3>Account Notifications</h3>
        <span class="unread-badge" *ngIf="unreadCount() > 0">{{ unreadCount() }}</span>
      </div>
      <div class="header-actions">
        <button
          class="mark-all-read-btn"
          *ngIf="unreadCount() > 0"
          (click)="markAllAsRead()"
          title="Mark all as read">
          <i class="fas fa-check-double"></i>
        </button>
      </div>
    </div>

    <!-- Urgent Notifications Alert -->
    <div class="urgent-alert" *ngIf="hasUnreadUrgent()">
      <i class="fas fa-exclamation-triangle"></i>
      <span>You have urgent notifications that require attention</span>
    </div>

    <!-- Notifications List -->
    <div class="notifications-list">
      <div
        *ngFor="let notification of displayNotifications()"
        class="notification-item"
        [class.unread]="!notification.read"
        [class]="getPriorityClass(notification.priority)"
        (click)="selectNotification(notification)">

        <!-- Notification Icon -->
        <div class="notification-icon" [style.color]="getNotificationColor(notification.type)">
          <i [class]="getNotificationIcon(notification.type)"></i>
        </div>

        <!-- Notification Content -->
        <div class="notification-content">
          <div class="notification-title">{{ notification.title }}</div>
          <div class="notification-message">{{ notification.message }}</div>
          <div class="notification-meta">
            <span class="notification-time">{{ formatTimestamp(notification.timestamp) }}</span>
            <span class="notification-category">{{ notification.category }}</span>
          </div>
        </div>

        <!-- Notification Actions -->
        <div class="notification-actions">
          <button
            *ngIf="!notification.read"
            class="action-btn mark-read-btn"
            (click)="markAsRead(notification, $event)"
            title="Mark as read">
            <i class="fas fa-check"></i>
          </button>
          <button
            class="action-btn delete-btn"
            (click)="deleteNotification(notification, $event)"
            title="Delete notification">
            <i class="fas fa-trash"></i>
          </button>
        </div>

        <!-- Unread Indicator -->
        <div class="unread-indicator" *ngIf="!notification.read"></div>
      </div>

      <!-- Empty State -->
      <div class="empty-state" *ngIf="displayNotifications().length === 0">
        <i class="fas fa-bell-slash"></i>
        <h4>No notifications</h4>
        <p>You're all caught up! New notifications will appear here.</p>
      </div>
    </div>
  </div>

  <!-- Notification Detail View -->
  <div class="notification-detail-view" *ngIf="selectedNotification()">
    <!-- Detail Header -->
    <div class="detail-header">
      <button class="back-btn" (click)="goBack()">
        <i class="fas fa-arrow-left"></i>
        <span>Back to notifications</span>
      </button>
    </div>

    <!-- Detail Content -->
    <div class="detail-content" *ngIf="selectedNotification() as notification">
      <!-- Notification Header -->
      <div class="detail-notification-header">
        <div class="detail-icon" [style.color]="getNotificationColor(notification.type)">
          <i [class]="getNotificationIcon(notification.type)"></i>
        </div>
        <div class="detail-title-section">
          <h2>{{ notification.title }}</h2>
          <div class="detail-meta">
            <span class="detail-time">{{ formatTimestamp(notification.timestamp) }}</span>
            <span class="detail-category">{{ notification.category }}</span>
            <span class="detail-priority" [class]="getPriorityClass(notification.priority)">
              {{ notification.priority }}
            </span>
          </div>
        </div>
      </div>

      <!-- Notification Message -->
      <div class="detail-message">
        <p>{{ notification.message }}</p>
      </div>

      <!-- Detailed Content -->
      <div class="detail-content-body" *ngIf="notification.detailContent">
        <div [innerHTML]="notification.detailContent"></div>
      </div>

      <!-- Action Button -->
      <div class="detail-actions" *ngIf="notification.actionUrl && notification.actionText">
        <a
          [routerLink]="notification.actionUrl"
          class="action-button"
          (click)="goBack()">
          {{ notification.actionText }}
          <i class="fas fa-arrow-right"></i>
        </a>
      </div>
    </div>
  </div>
</div>
