const { spawn } = require('child_process');
const chalk = require('chalk');
const fs = require('fs');
const path = require('path');

// First, fix the problematic package if it exists
const findUpPath = path.join(
  __dirname,
  'node_modules',
  'pkg-dir',
  'node_modules',
  'find-up',
  'index.js'
);

// Check if the file exists and fix it
if (fs.existsSync(findUpPath)) {
  // Read the file content
  let content = fs.readFileSync(findUpPath, 'utf8');

  // Replace the problematic import
  const oldImport = "import {locatePath, locatePathSync} from 'locate-path';";
  const newImport = "import locatePath from 'locate-path'; const locatePathSync = locatePath.sync;";

  // Replace the content if needed
  if (content.includes(oldImport)) {
    content = content.replace(oldImport, newImport);
    fs.writeFileSync(findUpPath, content, 'utf8');
  }
}

// Start the Angular dev server with super quiet mode and auto-open
const ngServe = spawn('ng', ['serve', '--configuration=optimized', '--hmr', '--live-reload', '--open'], {
  shell: true,
  stdio: ['inherit', 'pipe', 'pipe'],
  env: {
    ...process.env,
    // Suppress webpack warnings
    "NG_BUILD_MUTE": "true",
    "NODE_OPTIONS": "--no-warnings"
  }
});

// Flag to track if we've seen the server start message
let serverStarted = false;
let startTime = Date.now();

// Clear console at start
console.clear();
console.log(chalk.cyan('Starting Angular development server...'));

// Process stdout
ngServe.stdout.on('data', (data) => {
  const output = data.toString();

  // Only show the URL and success message, filter everything else
  if (output.includes('http://localhost:')) {
    // Extract the URL and display it in blue
    const match = output.match(/(http:\/\/localhost:[0-9]+\/)/);
    if (match && match[1]) {
      console.clear();
      console.log(chalk.blue.underline(match[1]));
      serverStarted = true;
    }
  }
  // Only show compilation success message
  else if (output.includes('Compiled successfully')) {
    if (serverStarted) {
      // Just keep the URL visible, don't add more text
    } else {
      console.clear();
      console.log(chalk.blue.underline('http://localhost:4200/'));
      serverStarted = true;
    }
  }
  // Show compilation errors (but nothing else)
  else if (output.includes('Failed to compile')) {
    console.log(chalk.red('✗ Failed to compile'));
  }
});

// Process stderr - completely suppress all stderr output
ngServe.stderr.on('data', (data) => {
  // Completely suppress all stderr output
  // Only show critical errors that would crash the server
  const errorOutput = data.toString();
  if (errorOutput.includes('Error:') &&
      !errorOutput.includes('Warning:') &&
      !errorOutput.includes('CommonJS')) {
    process.stderr.write(data);
  }
});

// Handle process exit
ngServe.on('close', (code) => {
  if (code !== 0) {
    console.log(chalk.red(`Angular CLI process exited with code ${code}`));
  }
  process.exit(code);
});
