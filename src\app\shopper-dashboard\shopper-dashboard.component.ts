import { Component, HostListener, AfterViewInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule } from '@angular/router';
import { SidebarService } from '../sidebar/sidebar.service';
import { TopNavbarComponent } from '../top-navbar/top-navbar.component';
import { SidebarComponent } from '../sidebar/sidebar.component';
import { Chart, DoughnutController, BarController, CategoryScale, LinearScale, ArcElement, BarElement, Tooltip } from 'chart.js';

// Register Chart.js components
Chart.register(DoughnutController, BarController, CategoryScale, LinearScale, ArcElement, BarElement, Tooltip);

interface Transaction {
  date: string;
  time: string;
  amount: number;
}

@Component({
  selector: 'app-shopper-dashboard',
  standalone: true,
  imports: [CommonModule, RouterModule, TopNavbarComponent, SidebarComponent],
  templateUrl: './shopper-dashboard.component.html',
  styleUrls: ['./shopper-dashboard.component.scss']
})
export class ShopperDashboardComponent implements AfterViewInit {
  user = {
    name: '<PERSON><PERSON><PERSON>',
    role: 'Co-founder & CEO',
    avatar: 'https://placehold.co/40x40'
  };

  stats = {
    transactions: 7623,
    turnover: 170632,
    mediumBasket: 124,
    customerRetention: 92
  };

  locations = [
    { name: '001 - SOUSSE', tickets: 3520 },
    { name: '002 - TUNISIA MALL', tickets: 1620 },
    { name: '003 - SFAX MALL', tickets: 7862 }
  ];

  tableData = [
    { ticketNumber: 10, product: 'Hat', customerId: '#20462', date: '13/05/2022', amount: 16, paymentMode: 'Cash', status: 'Loyal' },
    { ticketNumber: 9, product: 'Laptop', customerId: '#18933', date: '22/05/2022', amount: 2230, paymentMode: 'Cash', status: 'New' }
  ];

  transactions: Transaction[] = [
    { date: '14/06/2021', time: '14:24 AM', amount: 200 },
    { date: '24/05/2021', time: '22:30 AM', amount: 200 },
    { date: '11/04/2021', time: '16:20 AM', amount: 200 },
    { date: '11/04/2021', time: '16:20 AM', amount: 200 }
  ];

  isMobile: boolean = window.innerWidth <= 768;

  private chart: Chart | null = null;
  private monthChart: Chart | null = null;
  private savingChart: Chart | null = null;

  constructor(private sidebarService: SidebarService) {}

  ngAfterViewInit() {
    setTimeout(() => {
      this.initializeCharts();
    });
  }

  private initializeCharts() {
    // Initialize category expenses chart
    const ctxCategory = document.getElementById('expensesCategoryChart') as HTMLCanvasElement;
    if (ctxCategory) {
      if (this.chart) {
        this.chart.destroy();
      }

      const chartOptions = {
        responsive: false,
        maintainAspectRatio: false,
        width: 140,
        height: 100
      };

      this.chart = new Chart(ctxCategory, {
        type: 'doughnut',
        data: {
          labels: ['Food', 'Clothes', 'Leisure', 'Medicines'],
          datasets: [{
            data: [40, 30, 20, 10],
            backgroundColor: ['#A78BFA', '#F9A8D4', '#60A5FA', '#FBBF24'],
            borderWidth: 0
          }]
        },
        options: {
          ...chartOptions,
          cutout: '70%',
          plugins: {
            legend: { display: false },
            tooltip: { enabled: true }
          }
        }
      });
    }

    // Initialize monthly expenses chart
    const ctxMonth = document.getElementById('expensesMonthChart') as HTMLCanvasElement;
    if (ctxMonth) {
      if (this.monthChart) {
        this.monthChart.destroy();
      }

      this.monthChart = new Chart(ctxMonth, {
        type: 'bar',
        data: {
          labels: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul'],
          datasets: [{
            label: 'Expenses',
            data: [1200, 1500, 1700, 1400, 1600, 1300, 1544],
            backgroundColor: '#A78BFA',
            borderWidth: 0
          }]
        },
        options: {
          responsive: true,
          maintainAspectRatio: true,
          aspectRatio: 2,
          plugins: {
            legend: {
              display: false
            }
          },
          scales: {
            y: {
              beginAtZero: true,
              grid: {
                display: true
              }
            },
            x: {
              grid: {
                display: false
              }
            }
          }
        }
      });
    }

    // Initialize saving plan chart
    const ctxSaving = document.getElementById('savingPlanChart') as HTMLCanvasElement;
    if (ctxSaving) {
      if (this.savingChart) {
        this.savingChart.destroy();
      }

      const chartOptions = {
        responsive: false,
        maintainAspectRatio: false,
        width: 140,
        height: 70
      };

      this.savingChart = new Chart(ctxSaving, {
        type: 'doughnut',
        data: {
          labels: ['Product 1', 'Product 2', 'Product 3', 'Product 4'],
          datasets: [{
            data: [25, 25, 25, 25],
            backgroundColor: ['#FBBF24', '#F9A8D4', '#60A5FA', '#2ECC71'],
            borderWidth: 2,
            borderColor: '#fff'
          }]
        },
        options: {
          ...chartOptions,
          cutout: '70%',
          rotation: -90,
          circumference: 180,
          plugins: {
            legend: { display: false },
            tooltip: { enabled: true }
          }
        }
      });
    }
  }

  @HostListener('window:resize', ['$event'])
  onResize() {
    this.isMobile = window.innerWidth <= 768;
    this.initializeCharts(); // Reinitialize charts on window resize
  }

  toggleSidebar() {
    this.sidebarService.toggleSidebar();
  }
}