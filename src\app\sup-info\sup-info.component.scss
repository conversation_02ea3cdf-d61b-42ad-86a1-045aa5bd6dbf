/* Reset default styles */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

.container {
  display: flex;
  width: 100vw;
  height: 100vh;
  min-height: 100vh;
  max-height: 100vh;
  margin: 0;
  padding: 0;
  overflow: hidden !important;
  font-family: <PERSON><PERSON>, sans-serif;
  position: fixed !important;
  top: 0 !important;
  left: 0 !important;
  right: 0 !important;
  bottom: 0 !important;
  box-sizing: border-box !important;
}

/* Override global styles that might be constraining the height */
:host {
  height: 100vh !important;
  width: 100vw !important;
  overflow: hidden !important;
  display: block !important;
  position: fixed !important;
  top: 0 !important;
  left: 0 !important;
  right: 0 !important;
  bottom: 0 !important;
  box-sizing: border-box !important;
}

.left-section {
  flex: 0 0 50%;
  background: #ffffff;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  padding: 40px;
  margin: 0;
  height: 100vh !important;
  min-height: 100vh !important;
  max-height: 100vh !important;
  overflow-y: auto !important;
  box-sizing: border-box !important;
  position: relative !important;
}

.right-section {
  flex: 1;
  background: none !important;
  display: flex !important;
  flex-direction: column !important;
  justify-content: center !important;
  align-items: center !important;
  padding: 0 !important;
  margin: 0 !important;
  border-radius: 0 !important;
  position: relative !important;
  overflow: hidden !important;
  height: 100vh !important;
  min-height: 100vh !important;
  max-height: 100vh !important;
  width: 50vw !important;
  flex-shrink: 0 !important;
  box-sizing: border-box !important;
}

h1 {
  font-size: 2rem;
  font-weight: bold;
  margin-bottom: 10px;
  color: #333333 !important;
}

p {
  margin-bottom: 20px;
  color: #666;
  font-size: 0.9rem;
}

form {
  display: flex;
  flex-direction: column;
  gap: 20px;
  width: 100%;
  max-width: 400px;
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.form-group label {
  font-size: 0.9rem;
  font-weight: 500;
  color: #333;
  margin-bottom: 5px;
}

input, select {
  padding: 12px;
  border: 1px solid #ccc;
  border-radius: 20px;
  font-size: 1rem;
  width: 100%;
  box-sizing: border-box;
  background-color: #ffffff !important;
  color: #333333 !important;
}

input::placeholder {
  color: #999999 !important;
}

select {
  cursor: pointer;
}

select option {
  background-color: #ffffff;
  color: #333333;
}

.button-group {
  display: flex;
  gap: 15px;
  width: 100%;
  margin-top: 10px;
}

.create-account-btn {
  padding: 12px;
  background: #6b48ff;
  color: white;
  border: none;
  border-radius: 20px;
  font-size: 1rem;
  font-weight: bold;
  cursor: pointer;
  text-transform: uppercase;
  transition: background 0.3s;
  flex: 2; /* Takes more space */
}

.create-account-btn:hover {
  background: #5a3de6;
}

.create-account-btn:disabled {
  background: #ccc;
  cursor: not-allowed;
}

.skip-btn {
  padding: 12px;
  background: transparent;
  color: #6b48ff;
  border: 2px solid #6b48ff;
  border-radius: 20px;
  font-size: 1rem;
  font-weight: bold;
  cursor: pointer;
  text-transform: uppercase;
  transition: all 0.3s;
  flex: 1; /* Takes less space */
}

.skip-btn:hover {
  background: #6b48ff;
  color: white;
}

footer {
  margin-top: 20px;
  color: #666;
  font-size: 0.8rem;
}

/* Logo styling - covers entire right section */
.receeto-logo {
  width: 100% !important;
  height: 100% !important;
  background-image: url('/assets/images/Recetto-Login_Register-Logo.png');
  background-size: 100% 100% !important;
  background-position: center center !important;
  background-repeat: no-repeat !important;
  display: block !important;
  position: absolute !important;
  top: 0 !important;
  left: 0 !important;
  right: 0 !important;
  bottom: 0 !important;
  z-index: 10 !important; /* Higher z-index to stay above dark mode overlay */
  box-sizing: border-box !important;
  background-color: transparent !important; /* Ensure no background color */
}

/* Bottom navigation bar */
.bottom-nav {
  position: absolute !important;
  bottom: 20px !important;
  left: 50% !important;
  transform: translateX(-50%) !important;
  display: flex !important;
  gap: 30px !important;
  z-index: 2 !important;
  background: rgba(255, 255, 255, 0.1) !important;
  backdrop-filter: blur(10px) !important;
  padding: 10px 20px !important;
  border-radius: 25px !important;
  border: 1px solid rgba(255, 255, 255, 0.2) !important;
}

.bottom-nav .nav-link {
  color: #ffffff !important;
  text-decoration: none !important;
  font-size: 14px !important;
  font-weight: 500 !important;
  padding: 5px 10px !important;
  border-radius: 15px !important;
  transition: all 0.3s ease !important;
  white-space: nowrap !important;
}

.bottom-nav .nav-link:hover {
  background: rgba(255, 255, 255, 0.2) !important;
  transform: translateY(-2px) !important;
}

/* Success and Error Messages */
.error-message {
  color: #dc3545 !important;
  background-color: #f8d7da !important;
  border: 1px solid #f5c6cb !important;
  border-radius: 8px !important;
  padding: 10px !important;
  font-size: 0.9rem !important;
  margin-top: 10px !important;
  text-align: center !important;
  font-weight: 500 !important;
}

.success-message {
  color: #155724 !important;
  background-color: #d4edda !important;
  border: 1px solid #c3e6cb !important;
  border-radius: 8px !important;
  padding: 10px !important;
  font-size: 0.9rem !important;
  margin-top: 10px !important;
  text-align: center !important;
  font-weight: 500 !important;
}

/* Responsive design */
@media (max-width: 768px) {
  .container {
    flex-direction: column;
  }

  .left-section {
    flex: 0 0 100%;
    min-height: 100vh;
  }

  .right-section {
    display: none !important;
  }

  .bottom-nav {
    display: none !important;
  }
}

@media (max-width: 480px) {
  .left-section {
    padding: 20px;
  }

  h1 {
    font-size: 1.5rem;
  }

  form {
    gap: 15px;
  }

  input,
  select,
  button {
    padding: 12px;
    font-size: 14px;
  }

  .bottom-nav {
    display: none !important;
  }
}

/* Fix for Angular form classes */
.ng-untouched, .ng-pristine, .ng-invalid, .ng-valid {
  background-color: #ffffff !important;
  color: #333333 !important;
}

/* Dark mode protection for right-section */
:host {
  color: #333 !important;
}

.container {
  background-color: #ffffff !important;
}

.right-section {
  background-color: transparent !important;
  background: none !important;
}

/* Ensure logo stays visible in all modes */
.receeto-logo {
  background-color: transparent !important;
  opacity: 1 !important;
}

/* Ensure bottom nav stays visible */
.bottom-nav {
  background: rgba(255, 255, 255, 0.1) !important;
  backdrop-filter: blur(10px) !important;
  z-index: 20 !important;
}