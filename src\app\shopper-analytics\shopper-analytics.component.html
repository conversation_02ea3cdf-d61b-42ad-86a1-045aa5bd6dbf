<app-top-navbar></app-top-navbar>
<app-sidebar></app-sidebar>

<div class="analytics-container">
  <div class="card analytics-card fade-in">
    <!-- Dashboard Header -->
    <div class="dashboard-header">
      <h2 class="dashboard-title">Shopping Analytics</h2>
      <div class="date-range">
        <span class="icon">📅</span>
        <span>Current Period</span>
      </div>
    </div>

    <!-- Loading State -->
    <div class="loading-container" *ngIf="loading">
      <div class="loading-spinner"></div>
      <div>Loading analytics data...</div>
    </div>

    <ng-container *ngIf="!loading">
      <!-- Budget Progress Section -->
      <div class="progress-section">
        <div class="progress-item">
          <app-progress-circle
            [percentage]="analyticsData.budgetProgress.savingsGoalPercentage"
            [label]="'Until Your Savings Goal'"
            [type]="'savings'">
          </app-progress-circle>
        </div>
        <div class="progress-item">
          <app-progress-circle
            [percentage]="analyticsData.budgetProgress.budgetSpentPercentage"
            [label]="'Of Budget Spent'"
            [type]="'budget'">
          </app-progress-circle>
        </div>
      </div>
      
      <!-- Divider -->
      <div class="divider"></div>
      
      <!-- Monthly Spending Section -->
      <div class="spending-section">
        <div class="spending-info">
          <div class="spending-amount">
            {{ analyticsData.monthlySpending.amount }}
            <span class="currency">TND</span>
          </div>
          <div class="spending-change">
            <span class="percentage" [ngClass]="analyticsData.monthlySpending.percentageChange > 0 ? 'increase' : 'decrease'">
              {{ analyticsData.monthlySpending.percentageChange }}%
            </span>
            <span class="compared-period">compared to last month</span>
          </div>
          <div class="spending-label">Average Spending Per Month</div>
        </div>
        <div class="trend-chart-container">
          <app-trend-chart [data]="analyticsData.monthlySpending.trendData"></app-trend-chart>
        </div>
      </div>
      
      <!-- Divider -->
      <div class="divider"></div>
      
      <!-- Stats Grid -->
      <div class="stats-grid">
        <div class="stat-item">
          <div class="stat-label">Monthly Percentage</div>
          <div class="stat-value">{{ analyticsData.monthlySpending.percentage }}%</div>
          <div class="stat-change" [ngClass]="analyticsData.monthlySpending.percentageChange > 0 ? 'positive' : 'negative'">
            <span class="icon">{{ analyticsData.monthlySpending.percentageChange > 0 ? '↑' : '↓' }}</span> 
            {{ analyticsData.monthlySpending.percentageChange }}% from last month
          </div>
        </div>
        <div class="stat-item">
          <div class="stat-label">Top Category</div>
          <div class="stat-value">{{ analyticsData.expensesByCategory[0].name }}</div>
          <div class="stat-change">
            {{ analyticsData.expensesByCategory[0].amount }} TND ({{ analyticsData.expensesByCategory[0].percentage }}%)
          </div>
        </div>
        <div class="stat-item">
          <div class="stat-label">Top Product</div>
          <div class="stat-value">{{ analyticsData.expensesByProduct[0].name }}</div>
          <div class="stat-change">
            {{ analyticsData.expensesByProduct[0].amount }} TND ({{ analyticsData.expensesByProduct[0].percentage }}%)
          </div>
        </div>
      </div>
      
      <!-- Expenses by Category -->
      <div class="dashboard-card">
        <div class="card-header">
          <h3 class="card-title">Expenses By Category</h3>
        </div>
        <app-expense-list
          [expenses]="analyticsData.expensesByCategory"
          [suffix]="'Per Category'">
        </app-expense-list>
      </div>

      <!-- Expenses by Product -->
      <div class="dashboard-card">
        <div class="card-header">
          <h3 class="card-title">Expenses By Product</h3>
        </div>
        <app-expense-list
          [expenses]="analyticsData.expensesByProduct"
          [suffix]="'Per Product'">
        </app-expense-list>
      </div>

      <!-- Summary Value Section -->
      <div class="summary-value">
        {{ analyticsData.summaryValue }}
      </div>
    </ng-container>
  </div>
</div>