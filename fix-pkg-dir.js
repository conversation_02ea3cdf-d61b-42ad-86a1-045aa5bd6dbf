const fs = require('fs');
const path = require('path');

// Path to the problematic file
const findUpPath = path.join(
  __dirname,
  'node_modules',
  'pkg-dir',
  'node_modules',
  'find-up',
  'index.js'
);

// Check if the file exists
if (fs.existsSync(findUpPath)) {
  console.log('Found problematic file:', findUpPath);
  
  // Read the file content
  let content = fs.readFileSync(findUpPath, 'utf8');
  
  // Replace the problematic import
  const oldImport = "import {locatePath, locatePathSync} from 'locate-path';";
  const newImport = "import locatePath from 'locate-path'; const locatePathSync = locatePath.sync;";
  
  // Replace the content
  content = content.replace(oldImport, newImport);
  
  // Write the file back
  fs.writeFileSync(findUpPath, content, 'utf8');
  
  console.log('Fixed problematic import in:', findUpPath);
} else {
  console.log('Could not find problematic file:', findUpPath);
}
