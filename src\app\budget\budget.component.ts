import { Component, OnInit, OnDestroy } from '@angular/core';
import { CommonModule } from '@angular/common';
import { Router, RouterModule } from '@angular/router';
import { TopNavbarComponent } from '../top-navbar/top-navbar.component';
import { SidebarComponent } from '../sidebar/sidebar.component';
import { Budget, BudgetCategory } from '../models/budget.model';
import { BudgetService } from '../services/budget.service';
import { ThemeService } from '../services/theme.service';
import { Subject } from 'rxjs';
import { takeUntil } from 'rxjs/operators';

@Component({
  selector: 'app-budget',
  standalone: true,
  imports: [
    CommonModule,
    RouterModule,
    TopNavbarComponent,
    SidebarComponent
  ],
  templateUrl: './budget.component.html',
  styleUrls: ['./budget.component.scss']
})
export class BudgetComponent implements OnInit, OnDestroy {
  budgets: Budget[] = [];
  totalBudget: number = 0;
  totalSpent: number = 0;
  totalRemaining: number = 0;
  percentSpent: number = 0;
  percentRemaining: number = 0;
  isDarkMode: boolean = false;
  private destroy$ = new Subject<void>();

  constructor(
    private budgetService: BudgetService,
    private router: Router,
    private themeService: ThemeService
  ) {}

  ngOnInit(): void {
    // Subscribe to theme changes
    this.themeService.isDarkMode$
      .pipe(takeUntil(this.destroy$))
      .subscribe(isDark => {
        this.isDarkMode = isDark;
      });

    // Get budgets
    this.budgetService.getBudgets().subscribe(budgets => {
      this.budgets = budgets;

      // Calculate total budget and spending from all budget items
      this.calculateTotals(budgets);
    });
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  createNewBudget(): void {
    this.router.navigate(['/new-budget']);
  }

  viewBudgetDetails(categoryName: string): void {
    // Navigate to budget details page (to be implemented)
    console.log(`Viewing details for ${categoryName}`);
  }

  editBudget(budgetId: string): void {
    if (budgetId) {
      this.router.navigate(['/edit-budget', budgetId]);
    }
  }

  /**
   * Calculate the total budget, spent amount, and remaining amount from all budget items
   * @param budgets The list of budget items
   */
  private calculateTotals(budgets: Budget[]): void {
    if (budgets.length === 0) {
      // If there are no budgets, set all values to 0
      this.totalBudget = 0;
      this.totalSpent = 0;
      this.totalRemaining = 0;
      this.percentSpent = 0;
      this.percentRemaining = 0;
      return;
    }

    // Calculate total budget amount (sum of all budget items)
    this.totalBudget = budgets.reduce((sum, budget) => sum + budget.amount, 0);

    // Calculate total spent amount (sum of all spent amounts)
    this.totalSpent = budgets.reduce((sum, budget) => sum + (budget.spent || 0), 0);

    // Calculate total remaining amount
    this.totalRemaining = this.totalBudget - this.totalSpent;

    // Calculate percentage spent
    this.percentSpent = this.totalBudget > 0
      ? Math.round((this.totalSpent / this.totalBudget) * 100)
      : 0;

    // Calculate percentage remaining (opposite of percentSpent)
    this.percentRemaining = 100 - this.percentSpent;
  }
}
