export interface User {
  fullName: string;
  email: string;
  birthday: string;
  phoneNumber: string;
  governorate: string;
  gender: string;
  address: string;
  avatar: string;
  hasCustomAvatar: boolean;
  avatarPositionY: number;
  role: string;
  occupationType: 'Working' | 'Study' | 'Train' | 'Unemployed' | '';
  occupationPlace: string;
  showGenderPlaceholder: boolean;
  showAddressPlaceholder: boolean;
  showOccupationPlaceholder: boolean;
  showFullNamePlaceholder: boolean;
  lastShoppingDate: string;
}
