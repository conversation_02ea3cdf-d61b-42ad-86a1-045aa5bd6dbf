// progress-circle.component.scss
@import '../../../src/styles/variables';

.progress-circle {
  position: relative;
  width: 100%;
  max-width: 200px;
  margin: 0 auto;

  @media (max-width: $breakpoint-sm) {
    max-width: 150px;
  }
}

svg {
  width: 100%;
  height: 100%;
  transform: rotate(-90deg);
}

.circle-bg {
  fill: none;
  stroke: $light-grey;
  stroke-width: 2.8;
}

.circle {
  fill: none;
  stroke-width: 2.8;
  stroke-linecap: round;
  transition: stroke-dasharray 0.3s ease;
}

.savings .circle {
  stroke: $purple-color;
}

.budget .circle {
  stroke: $success-color;
}

.percentage {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  font-size: $font-size-xlarge;
  font-weight: $font-weight-bold;
  color: #333;

  @media (max-width: $breakpoint-sm) {
    font-size: $font-size-large;
  }
}

.label {
  text-align: center;
  margin-top: $spacing-sm;
  font-size: $font-size-base;
  color: $neutral-grey;
}