// This file has been moved to src/app/notification-system/notification.service.ts
// Please use the new location instead
// This file is kept for backward compatibility and will be removed in the future

import { Injectable } from '@angular/core';
import { BehaviorSubject, Observable } from 'rxjs';
import { Router } from '@angular/router';
import { NotificationService as NewNotificationService } from '../notification-system/notification.service';
import { map } from 'rxjs/operators';

// Re-export interfaces from the new location using 'export type'
export type { Notification, NotificationSetting, NotificationSettings } from '../notification-system/notification.service';

@Injectable({
  providedIn: 'root'
})
export class NotificationService {
  // This service now delegates to the new NotificationService
  constructor(private router: Router, private newService: NewNotificationService) {}

  // Expose the observables directly for backward compatibility
  get notificationSettings$(): Observable<any> {
    return this.newService.notificationSettings$;
  }

  get showNotifications$(): Observable<boolean> {
    return this.newService.showNotifications$;
  }

  // Delegate all methods to the new service
  toggleNotificationSetting(settingId: string): void {
    this.newService.toggleNotificationSetting(settingId);
  }

  toggleNotificationsPanel(): void {
    this.newService.toggleNotificationsPanel();
  }

  hideNotificationsPanel(): void {
    this.newService.hideNotificationsPanel();
  }

  getNotificationSettings(): Observable<any> {
    return this.newService.getNotificationSettings();
  }

  getShowNotifications(): Observable<boolean> {
    return this.newService.getShowNotifications();
  }

  getNotifications(): Observable<any[]> {
    return this.newService.getNotifications();
  }

  addNotification(notification: any): void {
    this.newService.addNotification(notification);
  }

  markNotificationAsRead(notificationId: string): void {
    this.newService.markNotificationAsRead(notificationId);
  }

  addPurchaseNotification(brandName: string, productName: string, price: number, quantity: number = 1): void {
    this.newService.addPurchaseNotification(brandName, productName, price, quantity);
  }
}
