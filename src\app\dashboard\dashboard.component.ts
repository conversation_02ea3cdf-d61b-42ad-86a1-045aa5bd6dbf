import { Component, OnInit, HostListener } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule } from '@angular/router';
import { SidebarService } from '../sidebar/sidebar.service';

@Component({
  selector: 'app-dashboard',
  standalone: true,
  imports: [CommonModule, RouterModule],
  templateUrl: './dashboard.component.html',
  styleUrls: ['./dashboard.component.scss']
})
export class DashboardComponent implements OnInit {
  user = {
    name: '<PERSON><PERSON><PERSON>',
    role: 'Co-founder & CEO',
    avatar: 'https://placehold.co/40x40'
  };

  stats = {
    transactions: 7623,
    turnover: 170632,
    mediumBasket: 124,
    customerRetention: 92
  };

  locations = [
    { name: '001 - SOUSSE', tickets: 3520 },
    { name: '002 - TUNISIA MALL', tickets: 1620 },
    { name: '003 - SFAX MALL', tickets: 7862 }
  ];

  tableData = [
    { ticketNumber: 10, product: 'Hat', customerId: '#20462', date: '13/05/2022', amount: 16, paymentMode: 'Cash', status: 'Loyal' },
    { ticketNumber: 9, product: 'Laptop', customerId: '#18933', date: '22/05/2022', amount: 2230, paymentMode: 'Cash', status: 'New' }
  ];

  isMobile = window.innerWidth <= 768;
  isMobileMenuOpen = false;

  constructor(private sidebarService: SidebarService) {}

  ngOnInit() {
    // Initialize any component data here
    console.log('Dashboard component initialized');
  }

  @HostListener('window:resize', ['$event'])
  onResize() {
    this.isMobile = window.innerWidth <= 768;
  }

  toggleSidebar() {
    this.sidebarService.toggleSidebar();
  }

  toggleMobileMenu() {
    this.isMobileMenuOpen = !this.isMobileMenuOpen;
  }

  closeMobileMenu() {
    this.isMobileMenuOpen = false;
  }

  @HostListener('document:click', ['$event'])
  onDocumentClick(event: MouseEvent) {
    const target = event.target as HTMLElement;
    const navbarRight = document.querySelector('.navbar-right');
    const avatarContainer = document.querySelector('.avatar-container');
    
    if (navbarRight && !navbarRight.contains(target) && !avatarContainer?.contains(target)) {
      this.closeMobileMenu();
    }
  }
}