<div class="container">
    <div class="form-section">
      <h1>Reset Password</h1>
      <form (ngSubmit)="onSubmit()" #resetPasswordForm="ngForm">
        <input type="password" placeholder="New Password" name="newPassword" [(ngModel)]="model.newPassword" required minlength="8" pattern="^(?=.*[a-z])(?=.*[A-Z])(?=.*\d).+$">
        <input type="password" placeholder="Confirm New Password" name="confirmPassword" [(ngModel)]="model.confirmPassword" required>
        <button type="submit" [disabled]="!resetPasswordForm.valid || model.newPassword !== model.confirmPassword">RESET PASSWORD</button>
      </form>
      <p class="error-message" *ngIf="errorMessage">{{ errorMessage }}</p>
    </div>
  </div>