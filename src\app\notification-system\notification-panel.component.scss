// Base panel styles
%panel-base {
  background-color: white;
  border-radius: 12px;
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.1),
              0 2px 8px rgba(0, 0, 0, 0.05),
              0 0 0 1px rgba(0, 0, 0, 0.05);
  width: 350px;
  max-height: 80vh;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  opacity: 0;
  transform: translateY(10px);
  pointer-events: none;
  transition: opacity 0.3s ease, transform 0.3s ease;
  will-change: opacity, transform;
  z-index: 9999; // Ensure high z-index by default
}

// Original notification panel for user profile page
.notification-panel {
  @extend %panel-base;

  &.flash-animation {
    animation: flash 1s ease;
  }

  &.show {
    opacity: 1;
    transform: translateY(0);
    pointer-events: auto;
  }

  &.dark-mode {
    background-color: #2d2d2d;
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.3),
                0 2px 8px rgba(0, 0, 0, 0.2),
                0 0 0 1px rgba(255, 255, 255, 0.05);

    .notification-header {
      background-color: #333;
      color: white;
      border-bottom: 1px solid #444;
    }

    .notification-item,
    .notification-Brand-Offers,
    .notification-Marketing,
    .notification-New-Partners,
    .notification-App-Updates {
      border-bottom: 1px solid #444;

      h4 {
        color: white;
      }

      p {
        color: #aaa;
      }
    }

    .toggle-switch {
      background-color: #444;

      &.active {
        background-color: #a855f7;
      }

      .toggle-slider {
        background-color: #222;
      }

      &.active .toggle-slider {
        background-color: white;
      }
    }
  }
}

// New notification panel for navbar
.notifications-panel {
  @extend %panel-base;

  &.flash-animation {
    animation: flash 1s ease;
  }

  &.showHide {
    opacity: 1;
    transform: translateY(0);
    pointer-events: auto;
  }

  &.dark-mode {
    background-color: #2d2d2d;
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.3),
                0 2px 8px rgba(0, 0, 0, 0.2),
                0 0 0 1px rgba(255, 255, 255, 0.05);

    .notification-header {
      background-color: #333;
      color: white;
      border-bottom: 1px solid #444;
    }

    .notification-list-item {
      border-bottom-color: #444;

      &:hover {
        background-color: rgba(255, 255, 255, 0.05);
      }
    }

    .notification-list-info {
      h4 {
        color: white;
      }

      p {
        color: #aaa;
      }

      small {
        color: #777;
      }
    }

    .empty-notifications p {
      color: #777;
    }
  }
}

.notification-header {
  padding: 15px;
  border-bottom: 1px solid #eee;
  display: flex;
  justify-content: space-between;
  align-items: center;

  h3 {
    margin: 0;
    font-size: 18px;
    font-weight: 600;
    color: #333;
  }

  .settings-icon {
    background: none;
    border: none;
    cursor: pointer;
    color: #666;
    font-size: 16px;
    padding: 5px;
    border-radius: 50%;
    transition: all 0.2s ease;

    &:hover {
      background-color: rgba(0, 0, 0, 0.05);
      color: #a855f7;
      transform: rotate(15deg);
    }

    .notifications-panel.dark-mode & {
      color: #aaa;

      &:hover {
        background-color: rgba(255, 255, 255, 0.1);
        color: #a855f7;
      }
    }
  }
}

.notification-content {
  max-height: 400px;
  overflow-y: auto;
}

// Base notification item styles
%notification-item-base {
  padding: 15px;
  border-bottom: 1px solid #eee;
  display: flex;
  justify-content: space-between;
  align-items: center;

  &:last-child {
    border-bottom: none;
  }

  .notification-info {
    flex: 1;

    h4 {
      margin: 0 0 5px;
      font-size: 16px;
      font-weight: 600;
      color: #333;
    }

    p {
      margin: 0;
      font-size: 14px;
      color: #666;
    }
  }

  .toggle-switch {
    width: 50px;
    height: 24px;
    background-color: #ddd;
    border-radius: 12px;
    position: relative;
    cursor: pointer;
    transition: background-color 0.2s ease;

    &.active {
      background-color: #a855f7;
    }

    .toggle-slider {
      width: 20px;
      height: 20px;
      background-color: white;
      border-radius: 50%;
      position: absolute;
      top: 2px;
      left: 2px;
      transition: transform 0.2s ease;
      box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
    }

    &.active .toggle-slider {
      transform: translateX(26px);
    }
  }
}

// For backward compatibility
.notification-item {
  @extend %notification-item-base;
}

// Specific notification types
.notification-Brand-Offers {
  @extend %notification-item-base;
}

.notification-Marketing {
  @extend %notification-item-base;
}

.notification-New-Partners {
  @extend %notification-item-base;
}

.notification-App-Updates {
  @extend %notification-item-base;
}

// Styling for the notification list and details in the notifications-panel
.notifications-panel {
  // Notification list
  .notification-list {
    max-height: 400px;
    overflow-y: auto;
  }

  .notification-list-item {
    padding: 12px 15px;
    border-bottom: 1px solid #eee;
    cursor: pointer;
    transition: background-color 0.2s ease;

    &:last-child {
      border-bottom: none;
    }

    &:hover {
      background-color: rgba(0, 0, 0, 0.05);
    }

    &.unread {
      position: relative;

      &::before {
        content: '';
        position: absolute;
        left: 5px;
        top: 50%;
        transform: translateY(-50%);
        width: 8px;
        height: 8px;
        border-radius: 50%;
        background-color: #a855f7;
      }

      h4 {
        font-weight: 600;
      }
    }
  }

  .notification-list-info {
    h4 {
      margin: 0 0 5px;
      font-size: 16px;
      color: #333;
    }

    p {
      margin: 0 0 5px;
      font-size: 14px;
      color: #666;
    }

    small {
      font-size: 12px;
      color: #999;
    }
  }

  .empty-notifications {
    padding: 20px;
    text-align: center;

    p {
      margin: 0;
      color: #999;
      font-size: 14px;
    }
  }

  .notification-detail {
    padding: 15px;

    .back-button {
      background: none;
      border: none;
      color: #666;
      font-size: 14px;
      padding: 5px 0;
      margin-bottom: 15px;
      cursor: pointer;
      display: flex;
      align-items: center;

      i {
        margin-right: 5px;
      }

      &:hover {
        color: #a855f7;
      }
    }

    .notification-detail-header {
      margin-bottom: 15px;

      h4 {
        font-size: 18px;
        font-weight: 600;
        margin: 0 0 5px;
        color: #333;
      }

      small {
        color: #999;
        font-size: 12px;
      }
    }

    .notification-detail-body {
      p {
        font-size: 14px;
        line-height: 1.5;
        margin-bottom: 15px;
        color: #555;
      }

      .notification-detail-content {
        padding: 15px;
        background-color: #f9f9f9;
        border-radius: 8px;
        font-size: 14px;
        line-height: 1.6;
        color: #555;
      }
    }
  }

  // Dark mode styles
  &.dark-mode {
    .notification-list-item {
      border-bottom-color: #444;

      &:hover {
        background-color: rgba(255, 255, 255, 0.05);
      }

      &.unread::before {
        background-color: #a855f7;
      }
    }

    .notification-list-info {
      h4 {
        color: white;
      }

      p {
        color: #aaa;
      }

      small {
        color: #777;
      }
    }

    .notification-detail {
      .back-button {
        color: #aaa;

        &:hover {
          color: #a855f7;
        }
      }

      .notification-detail-header {
        h4 {
          color: white;
        }

        small {
          color: #777;
        }
      }

      .notification-detail-body {
        p {
          color: #bbb;
        }

        .notification-detail-content {
          background-color: #333;
          color: #bbb;
        }
      }
    }

    .empty-notifications p {
      color: #777;
    }
  }
}

@keyframes flash {
  0%, 100% { box-shadow: 0 8px 20px rgba(0, 0, 0, 0.1), 0 2px 8px rgba(0, 0, 0, 0.05), 0 0 0 1px rgba(0, 0, 0, 0.05); }
  50% { box-shadow: 0 8px 20px rgba(168, 85, 247, 0.4), 0 2px 8px rgba(168, 85, 247, 0.2), 0 0 0 1px rgba(168, 85, 247, 0.2); }
}

// Mobile styles
@media (max-width: 768px) {
  .notification-panel,
  .notifications-panel {
    width: 100%;
    max-width: 350px;
    position: relative !important; // Force relative positioning
    margin-top: 30px !important; // Add space at the top
    margin-bottom: 80px !important; // Add space at the bottom
    z-index: 9999 !important; // Ensure it's above everything
  }

  // Ensure the notification panel is properly positioned in mobile mode
  .profile-container {
    display: flex;
    flex-direction: column;
    align-items: center;

    .notification-panel {
      order: 2; // Make sure it comes after the profile card
    }
  }
}
