import { Component, OnInit, HostListener } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule } from '@angular/router';
import { TrendChartComponent } from '../shared/trend-chart/trend-chart.component';
import { TopNavbarComponent } from '../top-navbar/top-navbar.component';
import { SidebarComponent } from '../sidebar/sidebar.component';
import { ProgressCircleComponent } from '../shared/progress-circle/progress-circle.component';
import { ExpenseListComponent } from '../shared/expense-list/expense-list.component';
import { SidebarService } from '../sidebar/sidebar.service';

interface ExpenseData {
  budgetProgress: {
    savingsGoalPercentage: number;
    budgetSpentPercentage: number;
  };
  monthlySpending: {
    amount: number;
    percentage: number;
    percentageChange: number;
    trendData: number[];
  };
  expensesByCategory: Array<{
    name: string;
    amount: number;
    percentage: number;
  }>;
  expensesByProduct: Array<{
    name: string;
    amount: number;
    percentage: number;
  }>;
  summaryValue: string;
}

@Component({
  selector: 'app-shopper-analytics',
  standalone: true,
  imports: [
    CommonModule,
    RouterModule,
    TrendChartComponent,
    TopNavbarComponent,
    SidebarComponent,
    ProgressCircleComponent,
    ExpenseListComponent
  ],
  templateUrl: './shopper-analytics.component.html',
  styleUrls: ['./shopper-analytics.component.scss']
})
export class ShopperAnalyticsComponent implements OnInit {
  loading = false;
  isMobile = window.innerWidth <= 768;
  isMobileMenuOpen = false;

  constructor(private sidebarService: SidebarService) {}

  ngOnInit() {
    // Initialize any component data here
    console.log('Shopper Analytics component initialized');
  }

  @HostListener('window:resize', ['$event'])
  onResize() {
    this.isMobile = window.innerWidth <= 768;
  }

  toggleSidebar() {
    this.sidebarService.toggleSidebar();
  }

  toggleMobileMenu() {
    this.isMobileMenuOpen = !this.isMobileMenuOpen;
  }

  closeMobileMenu() {
    this.isMobileMenuOpen = false;
  }

  @HostListener('document:click', ['$event'])
  onDocumentClick(event: MouseEvent) {
    const target = event.target as HTMLElement;
    const navbarRight = document.querySelector('.navbar-right');
    const avatarContainer = document.querySelector('.avatar-container');
    
    if (navbarRight && !navbarRight.contains(target) && !avatarContainer?.contains(target)) {
      this.closeMobileMenu();
    }
  }

  analyticsData: ExpenseData = {
    budgetProgress: {
      savingsGoalPercentage: 65,
      budgetSpentPercentage: 45
    },
    monthlySpending: {
      amount: 890,
      percentage: 12.5,
      percentageChange: 8.3,
      trendData: [65, 59, 80, 81, 56, 55, 40]
    },
    expensesByCategory: [
      { name: 'Groceries', amount: 450, percentage: 35 },
      { name: 'Electronics', amount: 300, percentage: 25 },
      { name: 'Clothing', amount: 240, percentage: 20 }
    ],
    expensesByProduct: [
      { name: 'Laptop', amount: 250, percentage: 30 },
      { name: 'Smartphone', amount: 200, percentage: 25 },
      { name: 'Headphones', amount: 100, percentage: 15 }
    ],
    summaryValue: '370 + 659'
  };
}