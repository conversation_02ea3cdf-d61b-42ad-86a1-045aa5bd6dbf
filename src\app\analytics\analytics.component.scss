/* Analytics Component SCSS migrated from CSS */
* {
  box-sizing: border-box;
}

.analytics-container {
  background-color: #E8F0FE;
  padding: 20px;
  max-width: 900px;
  margin: 0 auto;
  font-family: 'Arial', sans-serif;

  .user-avatar {
    display: none;
  }

  .overview-section {
    display: flex;
    justify-content: space-between;
    gap: 20px;
    margin-bottom: 20px;
    flex-wrap: wrap;

    .progress-card {
      display: flex;
      align-items: center;
      background: white;
      border-radius: 10px;
      padding: 20px;
      box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
      flex: 1;
      position: relative;
      flex-direction: column;
      text-align: center;

      .progress-circle {
        width: 100px;
        height: 100px;
        border-radius: 50%;
        border: 6px solid transparent;
        display: flex;
        justify-content: center;
        align-items: center;
        position: relative;
        margin: 0 0 15px;

        &.purple {
          border-color: #6a1b9a;
          background: rgba(106, 27, 154, 0.1);
        }

        &.green {
          border-color: #388e3c;
          background: rgba(56, 142, 60, 0.1);
        }

        .progress-text {
          font-size: 1.2rem;
          font-weight: bold;
          color: #6a1b9a;
        }
      }

      .progress-label {
        font-size: 0.9rem;
        color: #666;
      }

      .tooltip {
        position: absolute;
        top: -30px;
        background: #333;
        color: white;
        padding: 5px 10px;
        border-radius: 5px;
        font-size: 0.8rem;
        white-space: nowrap;
        z-index: 10;
      }
    }
  }

  .spending-card {
    background-color: #FFFFFF;
    border-radius: 10px;
    padding: 15px;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
    margin-bottom: 20px;

    h2 {
      color: #333333;
      font-size: 24px;
      font-weight: bold;
      margin-bottom: 5px;

      .trend-up,
      .trend-down {
        font-size: 14px;
        margin-left: 5px;
      }

      .trend-up {
        color: #FF4D4F;
      }

      .trend-down {
        color: #34C759;
      }
    }

    p {
      color: #666666;
      font-size: 14px;
      margin-bottom: 10px;
    }

    .trend-graph-placeholder {
      height: 50px;
      background-color: #F5F5F5;
      border-radius: 5px;
      display: flex;
      align-items: center;
      justify-content: center;
      color: #666666;
      font-size: 14px;
      cursor: pointer;

      &:hover {
        background-color: #E8F0FE;
      }
    }
  }

  .expenses-section {
    background-color: #FFFFFF;
    border-radius: 10px;
    padding: 15px;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
    margin-bottom: 20px;

    h3 {
      color: #333333;
      font-size: 18px;
      font-weight: bold;
      margin-bottom: 10px;
    }

    .expense-item {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 10px 0;
      border-bottom: 1px solid #E8F0FE;
      transition: background-color 0.2s;

      &:hover {
        background-color: #F5F5F5;
      }

      .expense-rank {
        color: #333333;
        font-weight: bold;
        flex: 0 0 10%;
      }

      .expense-name {
        color: #333333;
        flex: 0 0 40%;
      }

      .expense-amount {
        color: #666666;
        flex: 0 0 30%;
      }

      .expense-trend {
        flex: 0 0 20%;
        text-align: right;

        &.trend-up {
          color: #FF4D4F;
        }

        &.trend-down {
          color: #34C759;
        }
      }
    }
  }
}

/* Responsive */
@media (max-width: 768px) {
  .analytics-container {
    padding: 20px;
    max-width: 100%;

    .user-avatar {
      display: block;
      position: absolute;
      top: 20px;
      right: 20px;
      width: 40px;
      height: 40px;
      cursor: pointer;
      z-index: 1001;

      img {
        width: 100%;
        height: 100%;
        border-radius: 50%;
      }
    }

    .overview-section {
      flex-direction: column;
      gap: 15px;

      .progress-card {
        flex-direction: column;
        align-items: center;
        text-align: center;
        padding: 15px;
        margin: 10px 0;

        .progress-circle {
          width: 120px;
          height: 120px;
          margin: 0 0 15px;

          .progress-text {
            font-size: 18px;
          }
        }

        .progress-label {
          font-size: 14px;
        }
      }
    }

    .spending-card {
      padding: 15px;

      h2 {
        font-size: 20px;
      }

      p {
        font-size: 12px;
      }

      .trend-graph-placeholder {
        height: 120px;
        font-size: 12px;
      }
    }

    .expenses-section {
      padding: 15px;

      h3 {
        font-size: 16px;
      }

      .expense-item {
        padding: 8px 0;
        font-size: 14px;
      }
    }
  }
}

@media (max-width: 480px) {
  .analytics-container {
    padding: 10px;

    .progress-card {
      padding: 10px;

      .progress-circle {
        width: 100px;
        height: 100px;

        .progress-text {
          font-size: 16px;
        }
      }

      .progress-label {
        font-size: 12px;
      }
    }

    .spending-card {
      h2 {
        font-size: 18px;
      }

      p {
        font-size: 10px;
      }

      .trend-graph-placeholder {
        height: 100px;
        font-size: 10px;
      }
    }

    .expenses-section {
      h3 {
        font-size: 14px;
      }

      .expense-item {
        font-size: 12px;
      }
    }
  }
}