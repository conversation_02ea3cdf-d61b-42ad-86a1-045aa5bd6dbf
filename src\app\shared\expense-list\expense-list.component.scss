// expense-list.component.scss
@import '../../../../src/styles/variables';

.expense-list {
  margin-bottom: $spacing-lg;

  .list-title {
    font-size: 1.25rem;
    font-weight: $font-weight-bold;
    color: #2d3748;
    margin-bottom: $spacing-md;
  }

  .expense-items {
    display: flex;
    flex-direction: column;
    gap: $spacing-md;
  }

  .expense-item {
    display: flex;
    align-items: center;
    gap: $spacing-md;

    .expense-info {
      flex: 1;
      display: flex;
      justify-content: space-between;
      align-items: center;

      .name {
        font-size: $font-size-base;
        color: #2d3748;
      }

      .amount {
        font-size: $font-size-base;
        font-weight: $font-weight-medium;
        color: #2d3748;
      }
    }

    .progress-bar {
      width: 100px;
      height: 8px;
      background: $light-grey;
      border-radius: $border-radius-sm;
      overflow: hidden;

      .progress {
        height: 100%;
        background: $purple-color;
        transition: width 0.3s ease;
      }
    }

    .percentage {
      font-size: $font-size-base;
      color: $error-color;
      font-weight: $font-weight-medium;
      min-width: 60px;
      text-align: right;
    }
  }
}

@media (max-width: $breakpoint-sm) {
  .expense-list {
    margin-bottom: $spacing-md;

    .list-title {
      font-size: 1.125rem;
    }

    .expense-item {
      flex-direction: column;
      align-items: flex-start;
      gap: $spacing-sm;

      .progress-bar {
        width: 100%;
      }

      .percentage {
        text-align: left;
      }
    }
  }
}