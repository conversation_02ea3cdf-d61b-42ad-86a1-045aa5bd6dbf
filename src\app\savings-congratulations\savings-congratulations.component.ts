import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { Router, RouterModule } from '@angular/router';

@Component({
  selector: 'app-savings-congratulations',
  standalone: true,
  imports: [
    CommonModule,
    RouterModule
  ],
  templateUrl: './savings-congratulations.component.html',
  styleUrls: ['./savings-congratulations.component.scss']
})
export class SavingsCongratulationsComponent {
  constructor(private router: Router) {}

  done(): void {
    this.router.navigate(['/financial-savings']);
  }
}
