import { Injectable, signal, computed, effect } from '@angular/core';
import { Observable, of } from 'rxjs';
import { AccountNotification, AccountNotificationSettings, NotificationFilter } from '../../interfaces/account-notification';

@Injectable({
  providedIn: 'root'
})
export class AccountNotificationService {
  // Notification state signals
  private _notifications = signal<AccountNotification[]>([]);
  private _settings = signal<AccountNotificationSettings>({
    accountUpdates: true,
    securityAlerts: true,
    billingNotifications: true,
    featureAnnouncements: true,
    marketingOffers: false,
    systemMaintenance: true
  });
  private _selectedNotification = signal<AccountNotification | null>(null);
  private _isLoading = signal<boolean>(false);
  private _filter = signal<NotificationFilter>({});

  // Public readonly signals
  readonly notifications = this._notifications.asReadonly();
  readonly settings = this._settings.asReadonly();
  readonly selectedNotification = this._selectedNotification.asReadonly();
  readonly isLoading = this._isLoading.asReadonly();
  readonly filter = this._filter.asReadonly();

  // Computed signals
  readonly unreadCount = computed(() =>
    this._notifications().filter(n => !n.read).length
  );

  readonly urgentNotifications = computed(() =>
    this._notifications().filter(n => n.priority === 'urgent' && !n.read)
  );

  readonly filteredNotifications = computed(() => {
    const notifications = this._notifications();
    const currentFilter = this._filter();

    return notifications.filter(notification => {
      // Type filter
      if (currentFilter.type && !currentFilter.type.includes(notification.type)) {
        return false;
      }

      // Category filter
      if (currentFilter.category && !currentFilter.category.includes(notification.category)) {
        return false;
      }

      // Priority filter
      if (currentFilter.priority && !currentFilter.priority.includes(notification.priority)) {
        return false;
      }

      // Read status filter
      if (currentFilter.read !== undefined && notification.read !== currentFilter.read) {
        return false;
      }

      // Persistent filter
      if (currentFilter.persistent !== undefined && notification.persistent !== currentFilter.persistent) {
        return false;
      }

      // Date range filter
      if (currentFilter.dateRange) {
        const notificationDate = new Date(notification.timestamp);
        if (notificationDate < currentFilter.dateRange.from || notificationDate > currentFilter.dateRange.to) {
          return false;
        }
      }

      return true;
    });
  });

  readonly hasUnreadUrgent = computed(() =>
    this.urgentNotifications().length > 0
  );

  private _currentUserEmail = signal<string | null>(null);

  constructor() {
    this.initializeNotifications();

    // Effect to save notifications when they change
    effect(() => {
      const notifications = this._notifications();
      const userEmail = this._currentUserEmail();
      if (userEmail && notifications.length > 0) {
        this.saveNotificationsToStorage(notifications, userEmail);
      }
    });

    // Effect to load notifications when user changes
    effect(() => {
      const userEmail = this._currentUserEmail();
      if (userEmail) {
        this.loadNotificationsFromStorage(userEmail);
        this.loadSettingsFromStorage(userEmail);
      } else {
        this._notifications.set([]);
        this._selectedNotification.set(null);
      }
    });
  }

  private initializeNotifications(): void {
    // Initialize with some default system notifications
    const defaultNotifications: AccountNotification[] = [
      {
        id: 'welcome-001',
        userId: '',
        type: 'success',
        title: 'Welcome to Receeto!',
        message: 'Your account has been successfully created.',
        detailContent: 'Welcome to Receeto! We\'re excited to have you on board. Explore our features and start managing your receipts efficiently.',
        timestamp: new Date(),
        read: false,
        persistent: true,
        priority: 'medium',
        category: 'account',
        actionUrl: '/user-profile',
        actionText: 'Complete Profile'
      },
      {
        id: 'security-001',
        userId: '',
        type: 'info',
        title: 'Security Tip',
        message: 'Keep your account secure with a strong password.',
        detailContent: 'For your security, we recommend using a password that is at least 12 characters long and includes a mix of uppercase letters, lowercase letters, numbers, and special characters.',
        timestamp: new Date(Date.now() - 24 * 60 * 60 * 1000), // 1 day ago
        read: false,
        persistent: true,
        priority: 'low',
        category: 'security'
      }
    ];

    this._notifications.set(defaultNotifications);
  }

  // Public methods
  setCurrentUser(userEmail: string | null): void {
    this._currentUserEmail.set(userEmail);
  }

  addNotification(notification: Omit<AccountNotification, 'id' | 'userId' | 'timestamp'>): void {
    const userEmail = this._currentUserEmail();
    if (!userEmail) return;

    const newNotification: AccountNotification = {
      ...notification,
      id: this.generateNotificationId(),
      userId: userEmail,
      timestamp: new Date()
    };

    const currentNotifications = this._notifications();
    this._notifications.set([newNotification, ...currentNotifications]);
  }

  markAsRead(notificationId: string): void {
    const notifications = this._notifications();
    const updatedNotifications = notifications.map(n =>
      n.id === notificationId ? { ...n, read: true } : n
    );
    this._notifications.set(updatedNotifications);
  }

  markAllAsRead(): void {
    const notifications = this._notifications();
    const updatedNotifications = notifications.map(n => ({ ...n, read: true }));
    this._notifications.set(updatedNotifications);
  }

  deleteNotification(notificationId: string): void {
    const notifications = this._notifications();
    const updatedNotifications = notifications.filter(n => n.id !== notificationId);
    this._notifications.set(updatedNotifications);
  }

  selectNotification(notification: AccountNotification): void {
    this._selectedNotification.set(notification);
    if (!notification.read) {
      this.markAsRead(notification.id);
    }
  }

  clearSelection(): void {
    this._selectedNotification.set(null);
  }

  updateSettings(settings: Partial<AccountNotificationSettings>): void {
    const currentSettings = this._settings();
    const updatedSettings = { ...currentSettings, ...settings };
    this._settings.set(updatedSettings);

    const userEmail = this._currentUserEmail();
    if (userEmail) {
      this.saveSettingsToStorage(updatedSettings, userEmail);
    }
  }

  setFilter(filter: NotificationFilter): void {
    this._filter.set(filter);
  }

  clearFilter(): void {
    this._filter.set({});
  }

  // Storage methods
  private saveNotificationsToStorage(notifications: AccountNotification[], userEmail: string): void {
    try {
      const storageKey = `accountNotifications_${userEmail}`;
      localStorage.setItem(storageKey, JSON.stringify(notifications));
    } catch (error) {
      console.error('Failed to save notifications to storage:', error);
    }
  }

  private loadNotificationsFromStorage(userEmail: string): void {
    try {
      const storageKey = `accountNotifications_${userEmail}`;
      const stored = localStorage.getItem(storageKey);
      if (stored) {
        const notifications = JSON.parse(stored) as AccountNotification[];
        // Convert timestamp strings back to Date objects
        const processedNotifications = notifications.map(n => ({
          ...n,
          timestamp: new Date(n.timestamp),
          expiresAt: n.expiresAt ? new Date(n.expiresAt) : undefined
        }));
        this._notifications.set(processedNotifications);
      }
    } catch (error) {
      console.error('Failed to load notifications from storage:', error);
    }
  }

  private saveSettingsToStorage(settings: AccountNotificationSettings, userEmail: string): void {
    try {
      const storageKey = `accountNotificationSettings_${userEmail}`;
      localStorage.setItem(storageKey, JSON.stringify(settings));
    } catch (error) {
      console.error('Failed to save notification settings to storage:', error);
    }
  }

  private loadSettingsFromStorage(userEmail: string): void {
    try {
      const storageKey = `accountNotificationSettings_${userEmail}`;
      const stored = localStorage.getItem(storageKey);
      if (stored) {
        const settings = JSON.parse(stored) as AccountNotificationSettings;
        this._settings.set(settings);
      }
    } catch (error) {
      console.error('Failed to load notification settings from storage:', error);
    }
  }

  private generateNotificationId(): string {
    return `notification_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  // Utility methods
  getNotificationsByCategory(category: AccountNotification['category']): AccountNotification[] {
    return this._notifications().filter(n => n.category === category);
  }

  getNotificationsByType(type: AccountNotification['type']): AccountNotification[] {
    return this._notifications().filter(n => n.type === type);
  }

  getNotificationsByPriority(priority: AccountNotification['priority']): AccountNotification[] {
    return this._notifications().filter(n => n.priority === priority);
  }

  // System notification creators
  createWelcomeNotification(): void {
    this.addNotification({
      type: 'success',
      title: 'Welcome to Receeto!',
      message: 'Your account has been successfully created.',
      detailContent: 'Welcome to Receeto! We\'re excited to have you on board. Explore our features and start managing your receipts efficiently.',
      read: false,
      persistent: true,
      priority: 'medium',
      category: 'account',
      actionUrl: '/user-profile',
      actionText: 'Complete Profile'
    });
  }

  createSecurityNotification(message: string, detailContent?: string): void {
    this.addNotification({
      type: 'warning',
      title: 'Security Alert',
      message,
      detailContent,
      read: false,
      persistent: true,
      priority: 'high',
      category: 'security'
    });
  }

  createFeatureNotification(title: string, message: string, detailContent?: string): void {
    this.addNotification({
      type: 'info',
      title,
      message,
      detailContent,
      read: false,
      persistent: true,
      priority: 'medium',
      category: 'features'
    });
  }
}
