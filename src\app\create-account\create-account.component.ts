import { Component, OnInit, OnDestroy } from '@angular/core';
import { Router, ActivatedRoute } from '@angular/router';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { RouterModule } from '@angular/router';
import { ThemeService } from '../services/theme.service';
import { AuthService } from '../core/auth/auth.service';
import { CreationAccount } from '../interfaces/creation-account';
import { Subject, takeUntil } from 'rxjs';

@Component({
  selector: 'app-create-account',
  standalone: true,
  imports: [CommonModule ,FormsModule , RouterModule],
  templateUrl: './create-account.component.html',
  styleUrls: ['./create-account.component.css']
})
export class CreateAccountComponent implements OnInit, OnDestroy {
  accountData: CreationAccount = {
    firstName: '',
    lastName: '',
    email: '',
    password: '',
    terms: false
  };
  showPassword = false;
  errorMessage = '';
  successMessage = '';
  role: string = '';
  isDarkMode = false;
  private destroy$ = new Subject<void>();

  constructor(
    private router: Router,
    private route: ActivatedRoute,
    private themeService: ThemeService,
    private authService: AuthService
  ) {
    this.route.queryParams.subscribe(params => {
      this.role = params['role'] || '';
    });
  }

  ngOnInit(): void {
    // Subscribe to theme changes
    this.themeService.isDarkMode$
      .pipe(takeUntil(this.destroy$))
      .subscribe(isDark => {
        this.isDarkMode = isDark;
      });
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  togglePassword() {
    this.showPassword = !this.showPassword;
  }

  onSubmit() {
    if (this.accountData.firstName && this.accountData.lastName &&
        this.accountData.email && this.accountData.password && this.accountData.terms) {

      this.authService.register(this.accountData, this.role).subscribe({
        next: (user) => {
          console.log('Account created successfully:', user);
          this.errorMessage = '';
          this.successMessage = 'Account registered successfully! Redirecting to complete your profile...';

          // Navigate to supplementary info page after 2 seconds to show success message
          setTimeout(() => {
            this.router.navigate(['/sup-info'], { queryParams: { role: this.role } });
          }, 2000);
        },
        error: (err) => {
          console.error('Registration error:', err);
          this.successMessage = '';
          this.errorMessage = err.message || 'An error occurred during registration. Please try again.';
        }
      });
    } else {
      this.errorMessage = 'Please fill in all required fields and accept the terms.';
    }
  }
}