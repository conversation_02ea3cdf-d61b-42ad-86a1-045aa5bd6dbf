<div class="welcome-container">
  <!-- Development Reset Button -->
  <div class="dev-controls" *ngIf="isDevelopmentMode">
    <button class="reset-data-btn" (click)="resetAllData()" title="Reset all application data for development">
      <i class="fa fa-trash"></i>
      Reset All Data
    </button>
  </div>

  <div class="welcome-header">
    <h1>Welcome to Receeto</h1>
    <p class="welcome-subtitle">Choose your role to get started with our platform and unlock personalized features</p>
  </div>

  <div class="role-selection">
    <div class="role-container">
      <div class="role-box shopper-box">
        <div class="role-icon">
          <i class="fa fa-shopping-cart fa-2x"></i>
        </div>
        <h2>Shopper</h2>
        <div class="image-placeholder"></div>
      </div>
      <button class="select-btn" (click)="selectRole('shopper')">Start as Shopper</button>
    </div>

    <div class="role-container">
      <div class="role-box seller-box">
        <div class="role-icon">
          <i class="fa fa-store fa-2x"></i>
        </div>
        <h2>Seller</h2>
        <div class="image-placeholder"></div>
      </div>
      <button class="select-btn" (click)="selectRole('seller')">Start as Seller</button>
    </div>
  </div>

  <div class="footer">
    &copy; 2025 Receeto. All rights reserved.
  </div>

  <!-- Reset Confirmation Modal -->
  <div class="reset-confirmation-modal" *ngIf="showResetConfirmation">
    <div class="modal-overlay" (click)="cancelResetData()"></div>
    <div class="modal-content">
      <div class="modal-header">
        <h3>⚠️ Reset All Data</h3>
        <button class="close-btn" (click)="cancelResetData()">
          <i class="fa fa-times"></i>
        </button>
      </div>
      <div class="modal-body">
        <p><strong>This action will permanently delete:</strong></p>
        <ul>
          <li>All user accounts and profiles</li>
          <li>All budgets and financial data</li>
          <li>All loyalty cards</li>
          <li>All notifications and settings</li>
          <li>All application data</li>
        </ul>
        <p class="warning-text">⚠️ This action cannot be undone!</p>
      </div>
      <div class="modal-footer">
        <button class="confirm-reset-btn" (click)="confirmResetData()">
          <i class="fa fa-trash"></i>
          Yes, Reset All Data
        </button>
        <button class="cancel-btn" (click)="cancelResetData()">
          Cancel
        </button>
      </div>
    </div>
  </div>
</div>