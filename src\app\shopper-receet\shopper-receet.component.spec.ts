import { ComponentFixture, TestBed } from '@angular/core/testing';
import { FormsModule } from '@angular/forms';
import { TopNavbarComponent } from '../top-navbar/top-navbar.component';
import { SidebarComponent } from '../sidebar/sidebar.component';
import { ShopperReceetComponent } from './shopper-receet.component';

describe('ShopperReceetComponent', () => {
  let component: ShopperReceetComponent;
  let fixture: ComponentFixture<ShopperReceetComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [
        ShopperReceetComponent,
        FormsModule,
        TopNavbarComponent,
        SidebarComponent
      ]
    })
    .compileComponents();
    
    fixture = TestBed.createComponent(ShopperReceetComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});