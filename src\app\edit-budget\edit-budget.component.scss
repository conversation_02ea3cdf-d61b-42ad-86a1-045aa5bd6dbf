.edit-budget-container {
  padding: 20px;
  max-width: 600px;
  margin: 0 auto;
  font-family: 'Inter', sans-serif;
  
  // Dark mode styles
  &.dark-mode {
    color: #fff;
    
    .edit-budget-header h1 {
      color: #fff;
    }
    
    .budget-form {
      background-color: #2d2d2d;
      box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
      
      label {
        color: #fff;
      }
      
      .form-control {
        background-color: #333;
        color: #fff;
        border-color: #444;
        
        &::placeholder {
          color: #aaa;
        }
        
        &:focus {
          border-color: #6c5ce7;
          box-shadow: 0 0 0 2px rgba(108, 92, 231, 0.3);
        }
      }
      
      .period-option {
        background-color: #333;
        border-color: #444;
        color: #aaa;
        
        &:hover {
          background-color: #3a3a3a;
        }
        
        &.selected {
          background-color: #6c5ce7;
          color: white;
          border-color: #6c5ce7;
        }
      }
      
      .select-arrow {
        color: #aaa;
      }
      
      select {
        background-color: #333;
        color: #fff;
        border-color: #444;
        
        option {
          background-color: #333;
          color: #fff;
        }
      }
      
      .notification-settings {
        background-color: #333;
      }
      
      .notification-header {
        color: #fff;
      }
      
      .notification-description {
        color: #aaa;
      }
      
      .toggle-switch {
        label {
          background-color: #444;
          
          &:before {
            background-color: #666;
          }
        }
        
        input:checked + label {
          background-color: #6c5ce7;
          
          &:before {
            background-color: white;
          }
        }
      }
    }
    
    .loading-indicator,
    .not-found-message {
      color: #fff;
    }
  }
}

.edit-budget-header {
  margin-bottom: 20px;
  text-align: center;
  
  h1 {
    font-size: 24px;
    font-weight: 600;
    margin: 0;
  }
}

.loading-indicator,
.not-found-message {
  text-align: center;
  padding: 40px 20px;
  background-color: #fff;
  border-radius: 12px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
  margin-bottom: 20px;
  
  p {
    font-size: 18px;
    margin-bottom: 20px;
  }
}

.back-btn {
  background-color: #6c5ce7;
  color: white;
  border: none;
  border-radius: 8px;
  padding: 12px 25px;
  font-size: 16px;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s;
  
  &:hover {
    background-color: #5b4bc4;
  }
}

.budget-form {
  background-color: #fff;
  border-radius: 12px;
  padding: 20px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
}

.form-group {
  margin-bottom: 20px;
  
  label {
    display: block;
    font-weight: 500;
    margin-bottom: 8px;
    font-size: 16px;
  }
}

.form-control {
  width: 100%;
  padding: 12px 15px;
  border: 1px solid #e1e1e1;
  border-radius: 8px;
  font-size: 16px;
  background-color: #f9f9f9;
  
  &:focus {
    outline: none;
    border-color: #6c5ce7;
    box-shadow: 0 0 0 2px rgba(108, 92, 231, 0.2);
  }
  
  &::placeholder {
    color: #aaa;
  }
}

.period-options {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
}

.period-option {
  flex: 1;
  min-width: 100px;
  padding: 12px;
  text-align: center;
  background-color: #f9f9f9;
  border: 1px solid #e1e1e1;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s;
  
  &:hover {
    background-color: #f0f0f0;
  }
  
  &.selected {
    background-color: #6c5ce7;
    color: white;
    border-color: #6c5ce7;
  }
}

.category-select {
  position: relative;
  
  select {
    appearance: none;
    padding-right: 30px;
  }
  
  .select-arrow {
    position: absolute;
    right: 15px;
    top: 50%;
    transform: translateY(-50%);
    pointer-events: none;
    color: #666;
    font-size: 12px;
  }
}

.notification-settings {
  background-color: #f9f9f9;
  border-radius: 8px;
  padding: 15px;
  margin-top: 10px;
}

.notification-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 5px;
  font-weight: 500;
  cursor: pointer;
}

.notification-description {
  font-size: 14px;
  color: #666;
  margin-bottom: 15px;
}

.toggle-switch {
  position: relative;
  width: 50px;
  height: 24px;
  display: inline-block;
  
  input {
    opacity: 0;
    width: 0;
    height: 0;
    margin: 0;
    
    &:checked + label {
      background-color: #6c5ce7;
      
      &:before {
        transform: translateX(26px);
      }
    }
  }
  
  label {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: #ccc;
    border-radius: 34px;
    cursor: pointer;
    transition: 0.4s;
    margin: 0;
    
    &:before {
      position: absolute;
      content: "";
      height: 16px;
      width: 16px;
      left: 4px;
      bottom: 4px;
      background-color: white;
      border-radius: 50%;
      transition: 0.4s;
    }
  }
}

.form-actions {
  display: flex;
  justify-content: space-between;
  margin-top: 30px;
  
  .left-actions,
  .right-actions {
    display: flex;
    gap: 10px;
  }
}

.cancel-btn {
  background-color: transparent;
  color: #6c5ce7;
  border: none;
  padding: 12px 25px;
  font-size: 16px;
  font-weight: 500;
  cursor: pointer;
  transition: color 0.2s;
  
  &:hover {
    color: #5b4bc4;
  }
}

.save-btn {
  background-color: #6c5ce7;
  color: white;
  border: none;
  border-radius: 8px;
  padding: 12px 25px;
  font-size: 16px;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s;
  
  &:hover {
    background-color: #5b4bc4;
  }
}

.delete-btn {
  background-color: #e74c3c;
  color: white;
  border: none;
  border-radius: 8px;
  padding: 12px 25px;
  font-size: 16px;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s;
  
  &:hover {
    background-color: #c0392b;
  }
}

@media (max-width: 768px) {
  .period-options {
    flex-direction: column;
  }
  
  .form-actions {
    flex-direction: column;
    gap: 15px;
    
    .left-actions,
    .right-actions {
      width: 100%;
      justify-content: center;
    }
  }
}
