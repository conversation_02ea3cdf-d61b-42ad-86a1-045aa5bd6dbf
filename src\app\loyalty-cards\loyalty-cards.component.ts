import { Component, ElementRef, OnInit, ViewChild } from '@angular/core';
import { CommonModule } from '@angular/common';
import { Router, RouterModule } from '@angular/router';
import { TopNavbarComponent } from '../top-navbar/top-navbar.component';
import { SidebarComponent } from '../sidebar/sidebar.component';
import { LoyaltyCardService } from '../services/loyalty-card.service';
import { LoyaltyCard } from '../models/loyalty-card.model';
import { ThemeService } from '../services/theme.service';

interface LoyaltyCardWithUI extends LoyaltyCard {
  showOptions?: boolean;
}

@Component({
  selector: 'app-loyalty-cards',
  standalone: true,
  imports: [
    CommonModule,
    RouterModule,
    TopNavbarComponent,
    SidebarComponent
  ],
  templateUrl: './loyalty-cards.component.html',
  styleUrls: ['./loyalty-cards.component.scss']
})
export class LoyaltyCardsComponent implements OnInit {
  loyaltyCards: LoyaltyCardWithUI[] = [];
  isLoading = true;
  isDarkMode = false;
  selectedCardId: string | null = null;

  @ViewChild('barcodeFileInput') barcodeFileInput!: ElementRef<HTMLInputElement>;

  constructor(
    private loyaltyCardService: LoyaltyCardService,
    private router: Router,
    private themeService: ThemeService
  ) {}

  ngOnInit(): void {
    this.loadLoyaltyCards();
    this.themeService.isDarkMode$.subscribe(isDark => {
      this.isDarkMode = isDark;
    });

    // Close card options menu when clicking outside
    document.addEventListener('click', (event) => {
      if (this.loyaltyCards.some(card => card.showOptions)) {
        this.loyaltyCards.forEach(card => card.showOptions = false);
      }
    });
  }

  loadLoyaltyCards(): void {
    this.isLoading = true;
    this.loyaltyCardService.getLoyaltyCards().subscribe(cards => {
      this.loyaltyCards = cards.map(card => ({
        ...card,
        showOptions: false
      }));
      this.isLoading = false;
    });
  }

  addNewCard(): void {
    this.router.navigate(['/new-loyalty-card']);
  }

  getCardStyle(color: string): any {
    return {
      'background-color': color
    };
  }

  toggleCardOptions(event: Event, card: LoyaltyCardWithUI): void {
    event.stopPropagation(); // Prevent event bubbling

    // Close all other card options
    this.loyaltyCards.forEach(c => {
      if (c.id !== card.id) {
        c.showOptions = false;
      }
    });

    // Toggle the current card's options
    card.showOptions = !card.showOptions;
  }

  deleteCard(event: Event, card: LoyaltyCardWithUI): void {
    event.stopPropagation(); // Prevent event bubbling

    if (confirm(`Are you sure you want to delete the "${card.name}" loyalty card?`)) {
      this.loyaltyCardService.deleteLoyaltyCard(card.id);
      // Card will be removed from the list automatically via the subscription
    }
  }

  uploadBarcode(event: Event, card: LoyaltyCardWithUI): void {
    event.stopPropagation(); // Prevent event bubbling
    this.selectedCardId = card.id;
    this.barcodeFileInput.nativeElement.click();
  }

  onBarcodeFileSelected(event: Event): void {
    const input = event.target as HTMLInputElement;
    if (input.files && input.files.length > 0 && this.selectedCardId) {
      const file = input.files[0];
      const cardId = this.selectedCardId; // Store in local variable to ensure it's not null

      // Check if file is an image
      if (!file.type.startsWith('image/')) {
        alert('Please select an image file');
        return;
      }

      // Read the file as data URL
      const reader = new FileReader();
      reader.onload = (e) => {
        const imageUrl = e.target?.result as string;

        // Find the card and update its barcode image
        const cardToUpdate = this.loyaltyCards.find(card => card.id === cardId);
        if (cardToUpdate && imageUrl) {
          // Update the card with the new barcode image
          this.loyaltyCardService.updateCardBarcode(cardId, imageUrl);
        }

        // Reset the file input
        input.value = '';
        this.selectedCardId = null;
      };

      reader.readAsDataURL(file);
    }
  }
}
