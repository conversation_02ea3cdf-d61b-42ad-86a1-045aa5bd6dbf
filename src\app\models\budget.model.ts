export interface Budget {
  id: string;
  name: string;
  period: BudgetPeriod;
  amount: number;
  category: string;
  createdAt: Date;
  notifications: BudgetNotifications;
  spent?: number;
  remaining?: number;
  percentSpent?: number;
}

export interface BudgetCategory {
  name: string;
  amount: number;
  percentOfTotal: number;
  spent?: number;
  remaining?: number;
  percentSpent?: number;
}

export enum BudgetPeriod {
  OneMonth = 'OneMonth',
  ThreeMonths = 'ThreeMonths',
  SixMonths = 'SixMonths',
  OneYear = 'OneYear'
}

export interface BudgetNotifications {
  budgetOverrun: boolean;
  riskOfOverrun: boolean;
}
