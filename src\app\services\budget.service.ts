import { Injectable } from '@angular/core';
import { BehaviorSubject, Observable } from 'rxjs';
import { Budget, BudgetCategory, BudgetPeriod } from '../models/budget.model';
import { AppStorageService } from './app-storage.service';

@Injectable({
  providedIn: 'root'
})
export class BudgetService {
  private budgets: Budget[] = [];
  private budgetsSubject = new BehaviorSubject<Budget[]>([]);
  private readonly BUDGETS_STORAGE_KEY = 'budgets';
  private readonly BUDGET_CATEGORIES_KEY = 'budget_categories';

  constructor(private appStorage: AppStorageService) {
    // Load budgets from storage or initialize with sample data if none exist
    const storedBudgets = this.appStorage.getBudgets();

    if (storedBudgets && storedBudgets.length > 0) {
      this.budgets = storedBudgets;
    } else {
      // Initialize with sample data
      this.initializeSampleData();
    }

    // Update the subject with the loaded data
    this.budgetsSubject.next([...this.budgets]);
  }

  private initializeSampleData() {
    // Sample budget data - start with an empty array
    this.budgets = [];

    // Save to storage
    this.appStorage.saveBudgets(this.budgets);
  }

  getBudgets(): Observable<Budget[]> {
    return this.budgetsSubject.asObservable();
  }

  getBudgetById(id: string): Budget | undefined {
    return this.budgets.find(budget => budget.id === id);
  }

  addBudget(budget: Budget): void {
    // Generate a unique ID if not provided
    if (!budget.id) {
      budget.id = Date.now().toString();
    }

    // Set creation date
    budget.createdAt = new Date();

    // Calculate remaining and percentage
    if (budget.amount) {
      budget.spent = 0;
      budget.remaining = budget.amount;
      budget.percentSpent = 0;
    }

    // Add to budgets array
    this.budgets.push(budget);

    // Update categories
    this.addBudgetToCategories(budget);

    // Save to storage
    this.appStorage.saveBudgets(this.budgets);

    // Update the subject
    this.budgetsSubject.next([...this.budgets]);
  }

  private addBudgetToCategories(budget: Budget): void {
    if (!budget.category || !budget.amount) return;

    // Get current categories
    const categories = this.getBudgetCategories();

    // Check if category already exists
    const categoryIndex = categories.findIndex(c => c.name === budget.category + ' Budget');

    if (categoryIndex !== -1) {
      // Update existing category
      categories[categoryIndex].amount += budget.amount;
      categories[categoryIndex].remaining = (categories[categoryIndex].remaining || 0) + budget.amount;
      categories[categoryIndex].spent = categories[categoryIndex].spent || 0;
    } else {
      // Create new category
      categories.push({
        name: budget.category + ' Budget',
        amount: budget.amount,
        percentOfTotal: 0, // Will be calculated below
        spent: 0,
        remaining: budget.amount,
        percentSpent: 0
      });
    }

    // Recalculate percentages
    const totalAmount = categories.reduce((sum, category) => sum + category.amount, 0);
    categories.forEach(category => {
      category.percentOfTotal = Math.round((category.amount / totalAmount) * 100);
      category.spent = category.spent || 0;
      category.percentSpent = category.amount > 0 ? Math.round((category.spent / category.amount) * 100) : 0;
    });

    // Save updated categories
    this.appStorage.saveBudgetCategories(categories);
  }

  updateBudget(updatedBudget: Budget): void {
    const index = this.budgets.findIndex(budget => budget.id === updatedBudget.id);
    if (index !== -1) {
      const oldBudget = this.budgets[index];

      // Check if category has changed
      const categoryChanged = oldBudget.category !== updatedBudget.category;
      const amountChanged = oldBudget.amount !== updatedBudget.amount;

      // Update the budget
      this.budgets[index] = updatedBudget;

      // If category or amount changed, update budget categories
      if (categoryChanged || amountChanged) {
        this.updateBudgetCategories(oldBudget, updatedBudget);
      }

      // Save to storage
      this.appStorage.saveBudgets(this.budgets);

      // Update the subject
      this.budgetsSubject.next([...this.budgets]);
    }
  }

  private updateBudgetCategories(oldBudget: Budget, newBudget: Budget): void {
    // Get current categories
    const categories = this.getBudgetCategories();
    let updated = false;

    // If category changed, we need to handle both old and new categories
    if (oldBudget.category !== newBudget.category) {
      // Find the old category if it exists
      const oldCategoryIndex = categories.findIndex(c => c.name === oldBudget.category + ' Budget');
      if (oldCategoryIndex !== -1) {
        // Reduce the amount from the old category
        categories[oldCategoryIndex].amount -= oldBudget.amount;
        updated = true;
      }

      // Find the new category if it exists
      const newCategoryIndex = categories.findIndex(c => c.name === newBudget.category + ' Budget');
      if (newCategoryIndex !== -1) {
        // Add the amount to the new category
        categories[newCategoryIndex].amount += newBudget.amount;
        updated = true;
      } else {
        // Create a new category if it doesn't exist
        categories.push({
          name: newBudget.category + ' Budget',
          amount: newBudget.amount,
          percentOfTotal: 0, // Will be calculated below
          spent: 0,
          remaining: newBudget.amount,
          percentSpent: 0
        });
        updated = true;
      }
    }
    // If only amount changed, update the existing category
    else if (oldBudget.amount !== newBudget.amount) {
      const categoryIndex = categories.findIndex(c => c.name === newBudget.category + ' Budget');
      if (categoryIndex !== -1) {
        // Update the amount
        categories[categoryIndex].amount = categories[categoryIndex].amount - oldBudget.amount + newBudget.amount;
        categories[categoryIndex].spent = categories[categoryIndex].spent || 0;
        const spent = categories[categoryIndex].spent || 0;
        categories[categoryIndex].remaining = categories[categoryIndex].amount - spent;
        updated = true;
      }
    }

    // Recalculate percentages if any changes were made
    if (updated) {
      // Calculate total amount
      const totalAmount = categories.reduce((sum, category) => sum + category.amount, 0);

      // Update percentages
      categories.forEach(category => {
        category.percentOfTotal = Math.round((category.amount / totalAmount) * 100);
        category.spent = category.spent || 0;
        category.percentSpent = category.amount > 0 ? Math.round((category.spent / category.amount) * 100) : 0;
      });

      // Save updated categories
      this.appStorage.saveBudgetCategories(categories);
    }
  }

  deleteBudget(id: string): void {
    // Find the budget to be deleted
    const budgetToDelete = this.budgets.find(budget => budget.id === id);

    if (budgetToDelete) {
      // Remove budget from categories
      this.removeBudgetFromCategories(budgetToDelete);

      // Filter out the deleted budget
      this.budgets = this.budgets.filter(budget => budget.id !== id);

      // Save to storage
      this.appStorage.saveBudgets(this.budgets);

      // Update the subject
      this.budgetsSubject.next([...this.budgets]);
    }
  }

  private removeBudgetFromCategories(budget: Budget): void {
    // Get current categories
    const categories = this.getBudgetCategories();

    // Find the category for this budget
    const categoryIndex = categories.findIndex(c => c.name === budget.category + ' Budget');
    if (categoryIndex !== -1) {
      // Reduce the amount from the category
      categories[categoryIndex].amount -= budget.amount;

      // If category is now empty, remove it
      if (categories[categoryIndex].amount <= 0) {
        categories.splice(categoryIndex, 1);
      } else {
        // Update remaining and percentages
        categories[categoryIndex].spent = categories[categoryIndex].spent || 0;
        const spent = categories[categoryIndex].spent || 0;
        categories[categoryIndex].remaining = categories[categoryIndex].amount - spent;
        categories[categoryIndex].percentSpent = categories[categoryIndex].amount > 0
          ? Math.round((spent / categories[categoryIndex].amount) * 100)
          : 0;
      }

      // Recalculate percentages
      const totalAmount = categories.reduce((sum, category) => sum + category.amount, 0);
      categories.forEach(category => {
        category.percentOfTotal = Math.round((category.amount / totalAmount) * 100);
        category.spent = category.spent || 0;
      });

      // Save updated categories
      this.appStorage.saveBudgetCategories(categories);
    }
  }

  getBudgetCategories(): BudgetCategory[] {
    // Try to get from storage first
    const storedCategories = this.appStorage.getBudgetCategories();

    if (storedCategories && storedCategories.length > 0) {
      return storedCategories;
    }

    // Default categories if none in storage
    const defaultCategories = [
      {
        name: 'Grocery Budget',
        amount: 600,
        percentOfTotal: 40,
        spent: 240,
        remaining: 360,
        percentSpent: 40
      },
      {
        name: 'Leisure Budget',
        amount: 220,
        percentOfTotal: 15,
        spent: 220,
        remaining: 0,
        percentSpent: 100
      },
      {
        name: 'Clothes Budget',
        amount: 450,
        percentOfTotal: 30,
        spent: 135,
        remaining: 315,
        percentSpent: 30
      }
    ];

    // Save to storage
    this.appStorage.saveBudgetCategories(defaultCategories);

    return defaultCategories;
  }

  getAvailableCategories(): string[] {
    return ['Food', 'Transportation', 'Housing', 'Entertainment', 'Clothing', 'Healthcare', 'Education', 'Utilities', 'Other'];
  }

  /**
   * Add ticket expenses to a budget
   * @param budgetId The ID of the budget to add expenses to
   * @param amount The amount to add to the budget's spent value
   * @returns The updated budget or undefined if not found
   */
  addTicketsToBudget(budgetId: string, amount: number): Budget | undefined {
    const budgetIndex = this.budgets.findIndex(budget => budget.id === budgetId);
    if (budgetIndex === -1) return undefined;

    const budget = this.budgets[budgetIndex];

    // Update the budget's spent amount
    budget.spent = (budget.spent || 0) + amount;

    // Recalculate remaining and percentage
    budget.remaining = budget.amount - budget.spent;
    budget.percentSpent = Math.round((budget.spent / budget.amount) * 100);

    // Update the budget in the array
    this.budgets[budgetIndex] = budget;

    // Update the category
    this.updateCategorySpending(budget.category, amount);

    // Save to storage
    this.appStorage.saveBudgets(this.budgets);

    // Update the subject
    this.budgetsSubject.next([...this.budgets]);

    return budget;
  }

  /**
   * Update the spending for a category
   * @param categoryName The name of the category to update
   * @param amount The amount to add to the category's spent value
   */
  private updateCategorySpending(categoryName: string, amount: number): void {
    if (!categoryName) return;

    // Get current categories
    const categories = this.getBudgetCategories();

    // Find the category
    const categoryIndex = categories.findIndex(c => c.name === categoryName + ' Budget');
    if (categoryIndex !== -1) {
      // Get the category
      const category = categories[categoryIndex];

      // Update the spent amount
      category.spent = (category.spent || 0) + amount;

      // Recalculate remaining and percentage
      const spent = category.spent || 0;
      const budgetAmount = category.amount || 0;
      category.remaining = budgetAmount - spent;
      category.percentSpent = budgetAmount > 0 ? Math.round((spent / budgetAmount) * 100) : 0;

      // Save updated categories
      this.appStorage.saveBudgetCategories(categories);
    }
  }
}
