<app-top-navbar></app-top-navbar>
<app-sidebar></app-sidebar>

<div class="new-budget-container" [class.dark-mode]="isDarkMode">
  <div class="new-budget-header">
    <h1>New Budgets</h1>
  </div>

  <div class="budget-form" [class.dark-mode]="isDarkMode">
    <!-- Pending amount notification -->
    <div class="pending-amount-notification" *ngIf="pendingAmount > 0">
      <div class="notification-icon">
        <i class="fas fa-info-circle"></i>
      </div>
      <div class="notification-content">
        <h3>Tickets Ready to Add</h3>
        <p>You have selected tickets worth <strong>{{ pendingAmount }} TND</strong> that will be automatically added to this budget after creation.</p>
      </div>
    </div>

    <div class="form-group">
      <label>Budget Name</label>
      <input type="text" [(ngModel)]="budget.name" placeholder="Budget Name" class="form-control">
    </div>

    <div class="form-group">
      <label>Period</label>
      <div class="period-options">
        <div class="period-option" [class.selected]="budget.period === BudgetPeriod.OneMonth" (click)="budget.period = BudgetPeriod.OneMonth">
          1 month
        </div>
        <div class="period-option" [class.selected]="budget.period === BudgetPeriod.ThreeMonths" (click)="budget.period = BudgetPeriod.ThreeMonths">
          3 months
        </div>
        <div class="period-option" [class.selected]="budget.period === BudgetPeriod.SixMonths" (click)="budget.period = BudgetPeriod.SixMonths">
          6 months
        </div>
        <div class="period-option" [class.selected]="budget.period === BudgetPeriod.OneYear" (click)="budget.period = BudgetPeriod.OneYear">
          1 year
        </div>
      </div>
    </div>

    <div class="form-group">
      <label>Budget Amount</label>
      <input type="number" [(ngModel)]="budget.amount" placeholder="Example: 1000 TND" class="form-control">
    </div>

    <div class="form-group">
      <label>Category</label>
      <div class="category-select">
        <select [(ngModel)]="budget.category" class="form-control">
          <option value="" disabled selected>Select a category</option>
          <option *ngFor="let category of availableCategories" [value]="category">{{ category }}</option>
        </select>
        <div class="select-arrow">▼</div>
      </div>
    </div>

    <div class="form-group">
      <label>Notifications System</label>
      <div class="notification-settings" [class.expanded]="showNotificationSettings">
        <div class="notification-header" (click)="toggleNotificationSettings()">
          <span>Budget overrun</span>
          <div class="toggle-switch">
            <input type="checkbox" id="budgetOverrun" [(ngModel)]="budget.notifications.budgetOverrun">
            <label for="budgetOverrun"></label>
          </div>
        </div>
        <div class="notification-description">
          Warn When The Amount Has Exceeded The Budget
        </div>

        <div class="notification-header">
          <span>Risk of overrun</span>
          <div class="toggle-switch">
            <input type="checkbox" id="riskOverrun" [(ngModel)]="budget.notifications.riskOfOverrun">
            <label for="riskOverrun"></label>
          </div>
        </div>
        <div class="notification-description">
          Warn When Budget May Be Exceeded
        </div>
      </div>
    </div>

    <div class="form-actions">
      <button class="cancel-btn" (click)="cancel()">Cancel</button>
      <button class="confirm-btn" (click)="saveBudget()">Confirm</button>
    </div>
  </div>
</div>
