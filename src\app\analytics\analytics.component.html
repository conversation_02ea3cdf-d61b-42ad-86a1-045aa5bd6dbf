<div class="analytics-container">
  <div class="main-content">
    <div class="user-avatar" (click)="toggleSidebar()" *ngIf="isMobile">
      <!-- Removed avatar image -->
    </div>
    <!-- Overview Section -->
    <div class="overview-section">
      <div class="progress-card">
        <div class="progress-circle purple" (mouseover)="showTooltip('savings')" (mouseout)="hideTooltip()">
          <span class="progress-text">32%</span>
          <span class="tooltip" *ngIf="tooltip === 'savings'">32% of Savings Goal</span>
        </div>
        <span class="progress-label">Until Your Savings Goal</span>
      </div>
      <div class="progress-card">
        <div class="progress-circle green" (mouseover)="showTooltip('budget')" (mouseout)="hideTooltip()">
          <span class="progress-text">78%</span>
          <span class="progress-label">Of Budget Spent</span>
          <span class="tooltip" *ngIf="tooltip === 'budget'">78% of Budget Spent</span>
        </div>
      </div>
    </div>
    <!-- Average Spending -->
    <div class="spending-card">
      <h2>890 TND <span class="trend-up">1.5% ↑</span></h2>
      <p>Average Spending Per Month</p>
      <div class="trend-graph-placeholder">Trend Graph Placeholder</div>
    </div>
    <!-- Top Expenses by Category -->
    <div class="expenses-section">
      <h3>Top Expenses Per Category</h3>
      <div class="expense-item" (mouseover)="highlightItem($event)" (mouseout)="unhighlightItem($event)">
        <span class="expense-rank">1</span>
        <span class="expense-name">Food</span>
        <span class="expense-amount">4 506 TND</span>
        <span class="expense-trend trend-up">1.5% ↑</span>
      </div>
      <div class="expense-item" (mouseover)="highlightItem($event)" (mouseout)="unhighlightItem($event)">
        <span class="expense-rank">2</span>
        <span class="expense-name">Leisure</span>
        <span class="expense-amount">670 TND</span>
        <span class="expense-trend trend-up">1.5% ↑</span>
      </div>
      <div class="expense-item" (mouseover)="highlightItem($event)" (mouseout)="unhighlightItem($event)">
        <span class="expense-rank">3</span>
        <span class="expense-name">Bills</span>
        <span class="expense-amount">245 TND</span>
        <span class="expense-trend trend-up">0.5% ↑</span>
      </div>
      <div class="expense-item" (mouseover)="highlightItem($event)" (mouseout)="unhighlightItem($event)">
        <span class="expense-rank">4</span>
        <span class="expense-name">Health</span>
        <span class="expense-amount">70 TND</span>
        <span class="expense-trend trend-down">30% ↓</span>
      </div>
    </div>
    <!-- Top Expenses by Product -->
    <div class="expenses-section">
      <h3>Top Expenses Per Product</h3>
      <div class="expense-item" (mouseover)="highlightItem($event)" (mouseout)="unhighlightItem($event)">
        <span class="expense-rank">1</span>
        <span class="expense-name">Vegetables</span>
        <span class="expense-amount">2 656 TND</span>
        <span class="expense-trend trend-up">1.5% ↑</span>
      </div>
      <div class="expense-item" (mouseover)="highlightItem($event)" (mouseout)="unhighlightItem($event)">
        <span class="expense-rank">2</span>
        <span class="expense-name">Fruits</span>
        <span class="expense-amount">659 TND</span>
        <span class="expense-trend trend-up">1.5% ↑</span>
      </div>
      <div class="expense-item" (mouseover)="highlightItem($event)" (mouseout)="unhighlightItem($event)">
        <span class="expense-rank">3</span>
        <span class="expense-name">Internet</span>
        <span class="expense-amount">568 TND</span>
        <span class="expense-trend trend-up">0.5% ↑</span>
      </div>
      <div class="expense-item" (mouseover)="highlightItem($event)" (mouseout)="unhighlightItem($event)">
        <span class="expense-rank">4</span>
        <span class="expense-name">Cigarettes</span>
        <span class="expense-amount">351 TND</span>
        <span class="expense-trend trend-down">30% ↓</span>
      </div>
    </div>
  </div>
</div>