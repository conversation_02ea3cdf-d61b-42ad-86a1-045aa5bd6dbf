const { spawn } = require('child_process');
const chalk = require('chalk');
const fs = require('fs');
const path = require('path');

// First, fix the problematic package if it exists
const findUpPath = path.join(
  __dirname,
  'node_modules',
  'pkg-dir',
  'node_modules',
  'find-up',
  'index.js'
);

// Check if the file exists and fix it
if (fs.existsSync(findUpPath)) {
  // Read the file content
  let content = fs.readFileSync(findUpPath, 'utf8');

  // Replace the problematic import
  const oldImport = "import {locatePath, locatePathSync} from 'locate-path';";
  const newImport = "import locatePath from 'locate-path'; const locatePathSync = locatePath.sync;";

  // Replace the content if needed
  if (content.includes(oldImport)) {
    content = content.replace(oldImport, newImport);
    fs.writeFileSync(findUpPath, content, 'utf8');
  }
}

// Start the Angular dev server with optimized settings, auto-open, and no warnings
const ngServe = spawn('ng', ['serve', '--configuration=optimized', '--hmr', '--live-reload', '--open'], {
  shell: true,
  stdio: ['inherit', 'pipe', 'pipe'],
  env: {
    ...process.env,
    // Suppress webpack warnings
    "NG_BUILD_MUTE": "true",
    "NODE_OPTIONS": "--no-warnings"
  }
});

// Flag to track if we've seen the server start message
let serverStarted = false;
let startTime = Date.now();

// Clear console at start
console.clear();
console.log(chalk.cyan('Starting Angular development server...'));

// Process stdout
ngServe.stdout.on('data', (data) => {
  const output = data.toString();

  // Skip all the warning messages and build info
  if (output.includes("This is a simple server for use in testing") ||
      output.includes("It hasn't been reviewed for security issues") ||
      output.includes("DON'T USE IT FOR PRODUCTION") ||
      output.includes("*****") ||
      output.includes("Initial chunk files") ||
      output.includes("Lazy chunk files") ||
      output.includes("Build at:") ||
      output.includes("chunk {") ||
      output.includes("Hash:") ||
      output.includes("Warning:") ||
      output.includes("Running a server with --disable-host-check") ||
      output.includes("NOTICE: Hot Module Replacement (HMR) is enabled") ||
      output.includes("See https://webpack.js.org/guides/hot-module-replacement") ||
      output.includes("Generating browser application bundles") ||
      output.includes("CommonJS or AMD dependencies can cause optimization bailouts") ||
      output.includes("depends on") ||
      output.includes("For more info see:")) {
    return;
  }

  // Check if the output contains the localhost URL
  if (output.includes('http://localhost:')) {
    // Extract the URL and display it in blue
    const match = output.match(/(http:\/\/localhost:[0-9]+\/)/);
    if (match && match[1]) {
      console.clear();
      const buildTime = ((Date.now() - startTime) / 1000).toFixed(2);
      console.log(chalk.blue.underline(match[1]));
      console.log(chalk.gray(`Server started in ${buildTime}s`));
      serverStarted = true;
    }
  }
  // Only show compilation success message
  else if (output.includes('Compiled successfully')) {
    if (serverStarted) {
      console.clear();
      console.log(chalk.blue.underline('http://localhost:4200/'));
      console.log(chalk.green('✓ Compiled successfully'));
    } else {
      const buildTime = ((Date.now() - startTime) / 1000).toFixed(2);
      console.log(chalk.green(`✓ Compiled successfully in ${buildTime}s`));
    }
  }
  // Show compilation errors
  else if (output.includes('Failed to compile')) {
    console.log(chalk.red('✗ Failed to compile'));
  }
});

// Process stderr
ngServe.stderr.on('data', (data) => {
  const errorOutput = data.toString();

  // Filter out all warnings
  if (errorOutput.includes('Warning:') ||
      errorOutput.includes('warning') ||
      errorOutput.includes('This is a simple server for use in testing or debugging') ||
      errorOutput.includes('It hasn\'t been reviewed for security issues') ||
      errorOutput.includes('DON\'T USE IT FOR PRODUCTION!') ||
      errorOutput.includes('Running a server with --disable-host-check is a security risk') ||
      errorOutput.includes('Hot Module Replacement (HMR) is enabled') ||
      errorOutput.includes('CommonJS or AMD dependencies')) {
    return;
  }

  // Only show actual errors
  process.stderr.write(data);
});

// Handle process exit
ngServe.on('close', (code) => {
  if (code !== 0) {
    console.log(chalk.red(`Angular CLI process exited with code ${code}`));
  }
  process.exit(code);
});
