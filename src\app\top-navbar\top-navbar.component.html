<div class="top-navbar">
  <div class="navbar-left">
    <span class="navbar-title">Welcome Shopper</span>
  </div>

  <!-- Mobile Menu Button -->
  <div class="mobile-menu-button" *ngIf="isMobile">
    <div class="avatar-container">
      <div class="header-avatar avatar-placeholder" (click)="toggleSidebar()">
        <img *ngIf="authUser()?.avatar && hasCustomAvatar"
             [src]="authUser()?.avatar"
             alt="User Avatar"
             class="avatar-image"
             [ngStyle]="{'object-position': 'center ' + avatarPositionY + 'px'}">
        <svg *ngIf="!authUser()?.avatar || !hasCustomAvatar" class="avatar-icon" fill="currentColor" viewBox="0 0 24 24">
          <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm0 3c1.66 0 3 1.34 3 3s-1.34 3-3 3-3-1.34-3-3 1.34-3 3-3zm0 14.2c-2.5 0-4.71-1.28-6-3.22.03-1.99 4-3.08 6-3.08 1.99 0 5.97 1.09 6 3.08-1.29 1.94-3.5 3.22-6 3.22z"/>
        </svg>
      </div>
      <div class="dropdown-trigger" (click)="toggleMobileMenu()">
        <svg class="dropdown-arrow" [class.open]="isMobileMenuOpen" fill="currentColor" viewBox="0 0 24 24">
          <path d="M7 10l5 5 5-5H7z"/>
        </svg>
      </div>
    </div>
  </div>

  <div class="navbar-right" [class.open]="isMobileMenuOpen" (clickOutside)="closeMobileMenu()">
    <span class="date">{{ currentDate | date:'mediumDate' }}</span>
    <i class="fas fa-search" (click)="onSearchClick()" data-label="Search"></i>
    <i class="fas fa-info-circle" (click)="onInfoClick()" data-label="Information"></i>
    <i class="fas fa-bell" (click)="onNotificationsClick()" data-label="Notifications"></i>
    <i class="fas" [class.fa-moon]="!isDarkMode" [class.fa-sun]="isDarkMode" (click)="toggleTheme()" data-label="Dark Mode"></i>
    <div class="avatar-container" *ngIf="!isMobile">
      <div class="header-avatar avatar-placeholder">
        <img *ngIf="authUser()?.avatar && hasCustomAvatar"
             [src]="authUser()?.avatar"
             alt="User Avatar"
             class="avatar-image"
             [ngStyle]="{'object-position': 'center ' + avatarPositionY + 'px'}">
        <svg *ngIf="!authUser()?.avatar || !hasCustomAvatar" class="avatar-icon" fill="currentColor" viewBox="0 0 24 24">
          <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm0 3c1.66 0 3 1.34 3 3s-1.34 3-3 3-3-1.34-3-3 1.34-3 3-3zm0 14.2c-2.5 0-4.71-1.28-6-3.22.03-1.99 4-3.08 6-3.08 1.99 0 5.97 1.09 6 3.08-1.29 1.94-3.5 3.22-6 3.22z"/>
        </svg>
      </div>
      <div class="dropdown-trigger" (click)="toggleMobileMenu()">
        <svg class="dropdown-arrow" [class.open]="isMobileMenuOpen" fill="currentColor" viewBox="0 0 24 24">
          <path d="M7 10l5 5 5-5H7z"/>
        </svg>
      </div>
    </div>
  </div>
</div>

<!-- Notification Panel -->
<app-notification-panel></app-notification-panel>
<!-- The component name stays the same, but it's now using the one from notification-system -->