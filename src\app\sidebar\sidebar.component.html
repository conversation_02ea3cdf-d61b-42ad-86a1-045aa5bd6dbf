<div class="sidebar" #sidebar>
  <!-- Top section -->
  <div class="sidebar-top">
    <div class="logo">
      <a routerLink="/" class="logo-link">
        <div class="logo-image"></div>
      </a>
      <span class="arrow-icon" (click)="toggleDropdown()">▼</span>
    </div>
    <div class="dropdown-window" *ngIf="isDropdownOpen" #dropdownWindow>
      <div class="plan-comparison">
        <div class="plan-card normal">
          <div class="plan-header">
            <div class="plan-icon">👤</div>
            <h3>Normal User</h3>
            <span class="plan-price">Free</span>
          </div>
          <div class="plan-features">
            <ul>
              <li><span class="check">✓</span> Basic Dashboard</li>
              <li><span class="check">✓</span> Limited Analytics</li>
              <li><span class="disabled">×</span> Advanced Features</li>
              <li><span class="disabled">×</span> Priority Support</li>
            </ul>
          </div>
        </div>

        <div class="plan-card premium">
          <div class="premium-badge">RECOMMENDED</div>
          <div class="plan-header">
            <div class="plan-icon">👑</div>
            <h3>Premium User</h3>
            <span class="plan-price">$9.99/mo</span>
          </div>
          <div class="plan-features">
            <ul>
              <li><span class="check">✓</span> Advanced Dashboard</li>
              <li><span class="check">✓</span> Full Analytics</li>
              <li><span class="check">✓</span> Advanced Features</li>
              <li><span class="check">✓</span> Priority Support</li>
            </ul>
          </div>
        </div>
      </div>
    </div>
    <div class="sidebar-nav">
      <a routerLink="/shopper-dashboard" routerLinkActive="active" class="nav-item">
        <i class="icon fas fa-th-large"></i>
        <span>Dashboard</span>
      </a>

      <a routerLink="/shopper-analytics" routerLinkActive="active" class="nav-item">
        <i class="icon fas fa-chart-line"></i>
        <span>Analytics</span>
      </a>

      <a routerLink="/shopper-receet" routerLinkActive="active" class="nav-item">
        <i class="icon fas fa-table"></i>
        <span>Receipt</span>
      </a>

      <a routerLink="/budget" routerLinkActive="active" class="nav-item">
        <i class="icon fas fa-wallet"></i>
        <span>Budget</span>
      </a>

      <a routerLink="/financial-savings" routerLinkActive="active" class="nav-item">
        <i class="icon fas fa-piggy-bank"></i>
        <span>Financial Savings</span>
      </a>

      <a routerLink="/loyalty-cards" routerLinkActive="active" class="nav-item">
        <i class="icon fas fa-id-card"></i>
        <span>Loyalty Cards</span>
      </a>
      <a routerLink="/shopper-offer" routerLinkActive="active" class="nav-item">
        <i class="icon fas fa-tag"></i>
        <span>Offers</span>
      </a>
    </div>
  </div>

  <!-- Bottom section -->
  <div class="sidebar-bottom">
    <div class="upgrade-box">
      <p>Upgrade to Premium</p>
      <button class="upgrade-btn">Upgrade Now</button>
    </div>
    <div class="user-info" (click)="toggleUserInfo()">
      <div class="avatar-placeholder">
        <img *ngIf="authUser()?.avatar && hasCustomAvatar"
             [src]="authUser()?.avatar"
             alt="User Avatar"
             class="avatar-image"
             [ngStyle]="{'object-position': 'center ' + avatarPositionY + 'px'}">
        <svg *ngIf="!authUser()?.avatar || !hasCustomAvatar" class="avatar-icon" fill="currentColor" viewBox="0 0 24 24">
          <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm0 3c1.66 0 3 1.34 3 3s-1.34 3-3 3-3-1.34-3-3 1.34-3 3-3zm0 14.2c-2.5 0-4.71-1.28-6-3.22.03-1.99 4-3.08 6-3.08 1.99 0 5.97 1.09 6 3.08-1.29 1.94-3.5 3.22-6 3.22z"/>
        </svg>
      </div>
      <div class="user-details">
        <p class="user-name">{{ userFullName() || 'User' }}</p>
        <p class="user-role">{{ currentUser?.role || 'User' }}</p>
      </div>
    </div>

    <!-- User Info Dropdown -->
    <div class="user-info-dropdown" *ngIf="isUserInfoOpen" #userInfoDropdown>
      <div class="user-info-menu">
        <a routerLink="/user-profile" class="user-menu-item">
          <i class="fas fa-user"></i>
          <span>Profile</span>
        </a>
        <a href="https://receeto.com" target="_blank" class="user-menu-item">
          <i class="fas fa-link"></i>
          <span>Receeto Website</span>
        </a>
        <a href="#" class="user-menu-item">
          <i class="fas fa-shield-alt"></i>
          <span>Privacy Policy</span>
        </a>
        <button class="user-menu-item logout-btn" (click)="logout()">
          <i class="fas fa-sign-out-alt"></i>
          <span>Logout</span>
        </button>
      </div>
    </div>
  </div>
</div>
<div class="sidebar-overlay" #sidebarOverlay></div>