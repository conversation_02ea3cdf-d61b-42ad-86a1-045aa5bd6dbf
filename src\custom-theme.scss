// Custom Theming for Angular Material
// For more information: https://material.angular.io/guide/theming
@use '@angular/material' as mat;
// Plus imports for other components in your app.

// Include the common styles for Angular Material. We include this here so that you only
// have to load a single css file for Angular Material in your app.
// Be sure that you only ever include this mixin once!
@include mat.core();

// Define color palettes
$primary-palette: mat.define-palette(mat.$indigo-palette);
$accent-palette: mat.define-palette(mat.$pink-palette, A200, A100, A400);
$warn-palette: mat.define-palette(mat.$red-palette);

// Create the theme object
$theme: mat.define-light-theme((
  color: (
    primary: $primary-palette,
    accent: $accent-palette,
    warn: $warn-palette,
  )
));

// Apply the theme to all components
@include mat.all-component-themes($theme);

// Your custom styles can go here, after the Material theme setup
:root {
  --primary-color: #673ab7;
  --accent-color: #ffd740;
  --warn-color: #f44336;
  --background-color: #fafafa;
}

/* Rest of your custom styles */