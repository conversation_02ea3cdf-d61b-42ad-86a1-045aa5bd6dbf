export interface AccountNotification {
  id: string;
  userId: string; // Links notification to specific user account
  type: 'info' | 'warning' | 'success' | 'error' | 'promotion' | 'system';
  title: string;
  message: string;
  detailContent?: string; // Rich content for notification details
  timestamp: Date;
  read: boolean;
  persistent: boolean; // Whether notification stays after logout/login
  priority: 'low' | 'medium' | 'high' | 'urgent';
  category: 'account' | 'security' | 'billing' | 'features' | 'marketing' | 'system';
  actionUrl?: string; // Optional URL for notification action
  actionText?: string; // Text for action button
  expiresAt?: Date; // Optional expiration date
  metadata?: Record<string, any>; // Additional data
}

export interface AccountNotificationSettings {
  accountUpdates: boolean;
  securityAlerts: boolean;
  billingNotifications: boolean;
  featureAnnouncements: boolean;
  marketingOffers: boolean;
  systemMaintenance: boolean;
}

export interface NotificationFilter {
  type?: AccountNotification['type'][];
  category?: AccountNotification['category'][];
  priority?: AccountNotification['priority'][];
  read?: boolean;
  persistent?: boolean;
  dateRange?: {
    from: Date;
    to: Date;
  };
}
