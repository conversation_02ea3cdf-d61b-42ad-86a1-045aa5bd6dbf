.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.modal-content {
  background: var(--card-bg, white);
  border-radius: 12px;
  width: 90%;
  max-width: 600px;
  max-height: 90vh;
  overflow-y: auto;
  overflow-x: hidden;
  position: relative;
  border: 1px solid rgba(171, 85, 247, 0.2);
  box-shadow:
    0 10px 30px rgba(107, 72, 255, 0.15),
    0 6px 15px rgba(168, 85, 247, 0.1),
    0 0 0 1px rgba(171, 85, 247, 0.05),
    inset 0 0 0 1px rgba(255, 255, 255, 0.3);
  animation: modalGlow 1.5s ease-in-out infinite alternate;

  /* Custom scrollbar styling */
  scrollbar-width: thin;
  scrollbar-color: rgba(107, 72, 255, 0.3) transparent;

  &::-webkit-scrollbar {
    width: 6px;
    height: 6px;
  }

  &::-webkit-scrollbar-track {
    background: transparent;
    margin: 10px 0;
  }

  &::-webkit-scrollbar-thumb {
    background-color: rgba(107, 72, 255, 0.3);
    border-radius: 3px;
  }

  &::-webkit-scrollbar-thumb:hover {
    background-color: rgba(107, 72, 255, 0.5);
  }

  &.dark-mode {
    background-color: #2d2d2d;
    color: #fff;

    .modal-header {
      border-bottom: 1px solid rgba(171, 85, 247, 0.2);

      h2 {
        color: #fff;
      }

      .close-btn {
        color: #aaa;

        &:hover {
          color: #fff;
        }
      }
    }

    .modal-body {
      .budget-select {
        background-color: #333;
        color: #fff;
        border: 1px solid rgba(171, 85, 247, 0.2);
      }

      .selected-budget-info {
        background-color: #333;
        border: 1px solid rgba(171, 85, 247, 0.2);
      }

      .progress-bar {
        background-color: #444;
      }

      .no-budgets-warning {
        background-color: rgba(107, 72, 255, 0.1);
        border: 1px solid rgba(171, 85, 247, 0.2);
      }
    }

    .modal-footer {
      border-top: 1px solid rgba(171, 85, 247, 0.2);
    }
  }
}

.modal-header {
  padding: 20px;
  border-bottom: 1px solid rgba(171, 85, 247, 0.2);
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: linear-gradient(to bottom, rgba(107, 72, 255, 0.03), var(--card-bg, rgba(255, 255, 255, 0)));

  h2 {
    margin: 0;
    font-size: 1.5rem;
    color: var(--text-color, #333);
    font-weight: 600;
  }

  .close-btn {
    background: none;
    border: none;
    font-size: 1.2rem;
    cursor: pointer;
    color: var(--text-color, #666);
    padding: 5px;
    transition: all 0.2s ease;

    &:hover {
      color: var(--text-color, #333);
      opacity: 0.8;
      transform: scale(1.1);
    }
  }
}

.modal-body {
  padding: 20px;
  max-height: 60vh;
  overflow-y: auto;
  position: relative;

  .amount-info {
    margin-bottom: 25px;
    padding: 15px 20px;
    background: rgba(107, 72, 255, 0.03);
    border-radius: 10px;
    border: 1px solid rgba(171, 85, 247, 0.1);
    position: relative;

    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      width: 4px;
      height: 100%;
      background: linear-gradient(to bottom, #6B48FF, #A855F7);
      border-radius: 10px 0 0 10px;
    }

    p {
      margin: 0;
      color: var(--text-color, #333);
      font-size: 15px;

      strong {
        font-weight: 600;
        color: #6B48FF;
      }
    }
  }

  .no-budgets-warning {
    margin-bottom: 25px;
    padding: 15px 20px;
    background: rgba(255, 193, 7, 0.1);
    border-radius: 10px;
    border: 1px solid rgba(255, 193, 7, 0.2);
    position: relative;

    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      width: 4px;
      height: 100%;
      background: linear-gradient(to bottom, #FFC107, #FF9800);
      border-radius: 10px 0 0 10px;
    }

    p {
      margin: 0;
      color: var(--text-color, #333);
      font-size: 15px;

      i {
        color: #FF9800;
        margin-right: 8px;
      }
    }

    .create-budget-btn {
      margin-top: 15px;
      padding: 8px 16px;
      background: linear-gradient(135deg, #FFC107, #FF9800);
      color: white;
      border: none;
      border-radius: 6px;
      cursor: pointer;
      font-weight: 500;
      transition: all 0.3s ease;
      display: inline-flex;
      align-items: center;
      gap: 8px;

      &:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 8px rgba(255, 152, 0, 0.3);
      }

      i {
        color: white;
        margin-right: 0;
      }
    }
  }

  .budget-selection {
    margin-bottom: 20px;

    label {
      display: block;
      margin-bottom: 12px;
      font-weight: 500;
      color: var(--text-color, #333);
    }

    .budget-select {
      width: 100%;
      padding: 12px;
      border: 1px solid rgba(171, 85, 247, 0.2);
      border-radius: 8px;
      font-size: 14px;
      background-color: var(--input-bg, white);
      color: var(--input-text, #333);
      transition: all 0.3s ease;

      &:focus {
        outline: none;
        border-color: #6B48FF;
        box-shadow: 0 0 0 3px rgba(107, 72, 255, 0.1);
      }
    }
  }

  .selected-budget-info {
    background: rgba(107, 72, 255, 0.03);
    border: 1px solid rgba(171, 85, 247, 0.1);
    border-radius: 10px;
    padding: 15px;
    margin-bottom: 20px;

    p {
      margin: 8px 0;
      color: var(--text-color, #333);

      strong {
        font-weight: 600;
        color: #6B48FF;
      }
    }

    .warning {
      color: #e74c3c;
      font-weight: 500;
      margin-top: 15px;
      display: flex;
      align-items: center;

      i {
        margin-right: 8px;
      }
    }
  }

  .progress-bar {
    height: 8px;
    background-color: rgba(107, 72, 255, 0.1);
    border-radius: 4px;
    margin: 15px 0;
    overflow: hidden;

    .progress-fill {
      height: 100%;
      background: linear-gradient(to right, #6B48FF, #A855F7);
      border-radius: 4px;
      transition: width 0.3s ease;
    }
  }
}

.modal-footer {
  display: flex;
  justify-content: flex-end;
  padding: 20px;
  border-top: 1px solid rgba(171, 85, 247, 0.2);
  gap: 15px;

  button {
    padding: 10px 20px;
    border: none;
    border-radius: 8px;
    cursor: pointer;
    font-weight: 500;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
  }

  .cancel-btn {
    background-color: rgba(107, 72, 255, 0.1);
    color: #6B48FF;

    &:hover {
      background-color: rgba(107, 72, 255, 0.15);
      transform: translateY(-1px);
    }
  }

  .add-btn {
    background: linear-gradient(135deg, #6B48FF, #A855F7);
    color: white;
    box-shadow:
      0 4px 15px rgba(107, 72, 255, 0.25),
      0 2px 4px rgba(168, 85, 247, 0.2),
      inset 0 1px 1px rgba(255, 255, 255, 0.3);
    text-shadow: 0 1px 1px rgba(0, 0, 0, 0.2);
    position: relative;
    overflow: hidden;

    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: linear-gradient(to bottom, rgba(255, 255, 255, 0.2), rgba(255, 255, 255, 0));
      opacity: 0;
      transition: opacity 0.3s ease;
    }

    &:hover:not(:disabled) {
      transform: translateY(-2px);
      box-shadow:
        0 8px 20px rgba(107, 72, 255, 0.3),
        0 4px 8px rgba(168, 85, 247, 0.25),
        inset 0 1px 1px rgba(255, 255, 255, 0.4);

      &::before {
        opacity: 1;
      }
    }

    &:disabled {
      background: linear-gradient(135deg, #9e9e9e, #bdbdbd);
      color: rgba(255, 255, 255, 0.7);
      cursor: not-allowed;
      box-shadow: none;
      transform: none;
    }
  }
}

@keyframes modalGlow {
  from {
    box-shadow:
      0 10px 30px rgba(107, 72, 255, 0.15),
      0 6px 15px rgba(168, 85, 247, 0.1),
      0 0 0 1px rgba(171, 85, 247, 0.05),
      inset 0 0 0 1px rgba(255, 255, 255, 0.3);
  }
  to {
    box-shadow:
      0 15px 40px rgba(107, 72, 255, 0.2),
      0 8px 20px rgba(168, 85, 247, 0.15),
      0 0 0 1px rgba(171, 85, 247, 0.1),
      inset 0 0 0 1px rgba(255, 255, 255, 0.4);
  }
}
