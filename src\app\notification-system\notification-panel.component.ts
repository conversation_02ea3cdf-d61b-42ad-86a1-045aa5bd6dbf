import { <PERSON>mpo<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Host<PERSON><PERSON><PERSON>, ElementRef, AfterViewInit, Renderer2 } from '@angular/core';
import { CommonModule } from '@angular/common';
import { NotificationService, Notification } from './notification.service';
import { Subject, fromEvent } from 'rxjs';
import { takeUntil, debounceTime } from 'rxjs/operators';
import { ThemeService } from '../services/theme.service';
import { Router, NavigationEnd } from '@angular/router';

@Component({
  selector: 'app-notification-panel',
  templateUrl: './notification-panel.component.html',
  styleUrls: ['./notification-panel.component.scss'],
  standalone: true,
  imports: [CommonModule]
})
export class NotificationPanelComponent implements OnInit, AfterViewInit, OnDestroy {
  notificationSettings: any;
  showPanel = false; // Always start with panel hidden
  isDarkMode = false;
  isMobile = window.innerWidth <= 768;
  isUserProfilePage = false;
  isFlashing = false;
  private destroy$ = new Subject<void>();

  // Selected notification to display details
  selectedNotification: Notification | null = null;

  // Notifications from the service
  notifications: Notification[] = [];

  constructor(
    private notificationService: NotificationService,
    private themeService: ThemeService,
    private elementRef: ElementRef,
    private renderer: Renderer2,
    private router: Router
  ) {}

  ngOnInit(): void {
    // Subscribe to notification settings
    this.notificationService.notificationSettings$
      .pipe(takeUntil(this.destroy$))
      .subscribe(settings => {
        this.notificationSettings = settings;
      });

    // Subscribe to notifications
    this.notificationService.getNotifications()
      .pipe(takeUntil(this.destroy$))
      .subscribe(notifications => {
        this.notifications = notifications;
      });

    // Subscribe to panel visibility
    this.notificationService.showNotifications$
      .pipe(takeUntil(this.destroy$))
      .subscribe(show => {
        // If we're on the user profile page and the panel is being shown, flash it
        if (this.isUserProfilePage && show) {
          this.flashPanel();
        } else {
          // For other pages, update panel visibility
          this.showPanel = show;

          // Reset selected notification when panel is hidden
          if (!show) {
            this.selectedNotification = null;
          }
        }

        // If the panel is being shown, update its position
        if (show) {
          // Use setTimeout to ensure DOM is ready
          setTimeout(() => {
            this.updatePosition();
          }, 10);
        }
      });

    // Subscribe to theme changes
    this.themeService.isDarkMode$
      .pipe(takeUntil(this.destroy$))
      .subscribe(isDark => {
        this.isDarkMode = isDark;
      });

    // Listen for window resize with debounce for better performance
    const resizeObservable = fromEvent(window, 'resize').pipe(
      debounceTime(100),
      takeUntil(this.destroy$)
    );

    resizeObservable.subscribe(() => {
      this.onResize();
    });

    // Listen for scroll events to update position
    const scrollObservable = fromEvent(window, 'scroll').pipe(
      debounceTime(100),
      takeUntil(this.destroy$)
    );

    scrollObservable.subscribe(() => {
      if (this.isUserProfilePage) {
        this.updatePosition();
      }
    });

    // Check if we're on the user profile page
    this.checkUserProfilePage();

    // Subscribe to router events to detect navigation
    this.router.events
      .pipe(takeUntil(this.destroy$))
      .subscribe(event => {
        if (event instanceof NavigationEnd) {
          this.checkUserProfilePage();
        }
      });
  }

  checkUserProfilePage(): void {
    const isUserProfile = this.router.url === '/user-profile';
    this.isUserProfilePage = isUserProfile;

    // If we're on the user profile page, always show the panel
    if (isUserProfile) {
      this.showPanel = true;

      // Update position after a series of delays to ensure the DOM is fully rendered
      // First update immediately
      this.updatePosition();

      // Then update after short delay
      setTimeout(() => {
        this.updatePosition();
      }, 100);

      // Then update after DOM content should be fully loaded
      setTimeout(() => {
        this.updatePosition();
      }, 300);

      // Final position update after all animations and transitions should be complete
      setTimeout(() => {
        this.updatePosition();
      }, 500);
    }
  }

  // Flash the panel to indicate it's already visible
  flashPanel(): void {
    if (this.isFlashing) return;

    this.isFlashing = true;
    // Get the appropriate panel based on the current page
    const panel = this.isUserProfilePage
      ? this.elementRef.nativeElement.querySelector('.notification-panel')
      : this.elementRef.nativeElement.querySelector('.notifications-panel');

    if (panel) {
      this.renderer.addClass(panel, 'flash-animation');

      setTimeout(() => {
        this.renderer.removeClass(panel, 'flash-animation');
        this.isFlashing = false;
      }, 1000);
    } else {
      this.isFlashing = false;
    }
  }

  ngAfterViewInit(): void {
    this.updatePosition();
  }

  onResize(): void {
    const wasMobile = this.isMobile;
    this.isMobile = window.innerWidth <= 768;

    // Always update position when on user profile page or when mobile state changes
    if (this.isUserProfilePage || wasMobile !== this.isMobile) {
      // Use requestAnimationFrame for better performance
      requestAnimationFrame(() => {
        this.updatePosition();

        // Series of updates to ensure proper positioning after resize
        setTimeout(() => {
          this.updatePosition();
        }, 100);

        setTimeout(() => {
          this.updatePosition();
        }, 300);

        setTimeout(() => {
          this.updatePosition();
        }, 500);
      });
    }
  }

  updatePosition(): void {
    // Handle both panel types
    const userProfilePanel = this.elementRef.nativeElement.querySelector('.notification-panel');
    const navbarPanel = this.elementRef.nativeElement.querySelector('.notifications-panel');

    // Position the user profile panel if we're on the user profile page
    if (this.isUserProfilePage && userProfilePanel) {
      // Make sure it's visible
      this.renderer.setStyle(userProfilePanel, 'opacity', '1');
      this.renderer.setStyle(userProfilePanel, 'transform', 'translateY(0)');
      this.renderer.setStyle(userProfilePanel, 'pointer-events', 'auto');

      const profileCard = document.getElementById('profile-card');
      if (profileCard) {
        const profileCardRect = profileCard.getBoundingClientRect();

        if (this.isMobile) {
          // In mobile mode, position below the profile card
          // First reset any previous styles
          this.renderer.removeStyle(userProfilePanel, 'right');
          this.renderer.removeStyle(userProfilePanel, 'top');
          this.renderer.removeStyle(userProfilePanel, 'left');
          this.renderer.removeStyle(userProfilePanel, 'transform');

          // Calculate the position based on the profile card's full content
          const profileContent = document.querySelector('.profile-content');
          let bottomPosition = 0;

          if (profileContent) {
            const profileContentRect = profileContent.getBoundingClientRect();
            bottomPosition = profileContentRect.bottom + window.scrollY + 30; // Add some spacing
          } else {
            // Fallback if profile content not found
            bottomPosition = profileCardRect.bottom + window.scrollY + 30;
          }

          // Set new styles for mobile with higher z-index
          this.renderer.setStyle(userProfilePanel, 'position', 'absolute');
          this.renderer.setStyle(userProfilePanel, 'top', `${bottomPosition}px`);
          this.renderer.setStyle(userProfilePanel, 'left', '50%');
          this.renderer.setStyle(userProfilePanel, 'transform', 'translateX(-50%)');
          this.renderer.setStyle(userProfilePanel, 'width', 'calc(100% - 40px)');
          this.renderer.setStyle(userProfilePanel, 'max-width', '350px');
          this.renderer.setStyle(userProfilePanel, 'z-index', '9999'); // Much higher z-index to appear above inspect element
          this.renderer.setStyle(userProfilePanel, 'margin-bottom', '80px'); // Increased bottom margin

          // Force a reflow to ensure the panel is properly positioned
          userProfilePanel.getBoundingClientRect();
        } else {
          // In desktop mode, position to the right of the profile card
          // First reset any previous styles
          this.renderer.removeStyle(userProfilePanel, 'max-width');
          this.renderer.removeStyle(userProfilePanel, 'transform');

          // Set new styles for desktop
          this.renderer.setStyle(userProfilePanel, 'position', 'absolute');
          this.renderer.setStyle(userProfilePanel, 'top', `${profileCardRect.top}px`);
          this.renderer.setStyle(userProfilePanel, 'left', `${profileCardRect.right + 20}px`);
          this.renderer.setStyle(userProfilePanel, 'width', '350px');
          this.renderer.setStyle(userProfilePanel, 'z-index', '9999'); // Higher z-index
        }
      }
    }

    // Position the navbar panel if we're not on the user profile page
    if (!this.isUserProfilePage && navbarPanel && this.showPanel) {
      // Make sure it's visible
      this.renderer.setStyle(navbarPanel, 'opacity', '1');
      this.renderer.setStyle(navbarPanel, 'transform', 'translateY(0)');
      this.renderer.setStyle(navbarPanel, 'pointer-events', 'auto');

      // Position under the notification icon
      const bellIcon = document.querySelector('.fas.fa-bell');
      if (bellIcon) {
        const bellRect = bellIcon.getBoundingClientRect();

        // Reset any previous styles
        this.renderer.removeStyle(navbarPanel, 'max-width');
        this.renderer.removeStyle(navbarPanel, 'transform');

        // Set new styles
        this.renderer.setStyle(navbarPanel, 'position', 'fixed');
        this.renderer.setStyle(navbarPanel, 'top', `${bellRect.bottom + 10}px`);
        this.renderer.setStyle(navbarPanel, 'right', '20px');
        this.renderer.setStyle(navbarPanel, 'left', 'auto');
        this.renderer.setStyle(navbarPanel, 'width', '350px');
        this.renderer.setStyle(navbarPanel, 'z-index', '1000');
      }
    }
  }

  @HostListener('document:click', ['$event'])
  onDocumentClick(event: MouseEvent): void {
    // If we're on the user profile page, don't close the panel on outside clicks
    if (this.isUserProfilePage) {
      return;
    }

    // Close panel when clicking outside, but not when clicking the bell icon
    const target = event.target as HTMLElement;
    const bellIcon = document.querySelector('.fas.fa-bell');
    const navbarPanel = this.elementRef.nativeElement.querySelector('.notifications-panel');

    // Check if click is outside both the panel and the bell icon
    if (navbarPanel &&
        !navbarPanel.contains(target) &&
        bellIcon && !bellIcon.contains(target) &&
        this.showPanel) {
      // Reset selected notification and hide panel
      this.selectedNotification = null;
      this.notificationService.hideNotificationsPanel();
    }
  }

  toggleNotification(settingId: string): void {
    this.notificationService.toggleNotificationSetting(settingId);
  }

  // Navigate to user profile page for notification settings
  navigateToSettings(): void {
    this.router.navigate(['/user-profile']);
    this.notificationService.hideNotificationsPanel();
  }

  // Select a notification to view its details
  selectNotification(notification: Notification): void {
    this.selectedNotification = notification;

    // Mark notification as read
    if (!notification.read) {
      // Use the service to mark as read
      this.notificationService.markNotificationAsRead(notification.id);
    }
  }

  // Go back to the notification list
  backToNotificationList(): void {
    this.selectedNotification = null;
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }
}
