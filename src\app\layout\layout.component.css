.main-content {
  margin-left: 250px;
  padding: 20px;
  height: 100%; /* Use height instead of min-height */
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  flex-grow: 1;
}

.mobile-user-info {
  display: none;
}

@media (max-width: 768px) {
  .main-content {
    margin-left: 0 !important;
    padding: 20px !important;
    width: 100% !important;
  }

  .mobile-user-info {
    display: block;
    position: absolute;
    top: 20px;
    right: 20px;
    z-index: 1001;
  }

  .mobile-user-avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    cursor: pointer;
  }
}