.ticket-history-container {
  padding: 1.5rem;
  max-width: 1400px; // Increased for wider content
  margin: 0 auto;
}

.ticket-history-header {
  margin-bottom: 1.5rem;
  h2 {
    font-size: 1.75rem;
    font-weight: 700;
    color: #1f2937;
  }
}

.tickets-list {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.ticket-card {
  background: white;
  border-radius: 16px;
  padding: 1.5rem;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
  border: 1px solid #e5e7eb;
  transition: all 0.3s ease;
  cursor: pointer;

  &:hover {
    transform: scale(1.01);
    background-color: #f9fafb;
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.12);
  }

  .ticket-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 1rem;

    .merchant-info {
      display: flex;
      gap: 1rem;
      align-items: center;

      .merchant-logo {
        width: 56px;
        height: 56px;
        border-radius: 10px;
        object-fit: cover;
        box-shadow: 0 2px 6px rgba(0, 0, 0, 0.08);
      }

      .merchant-details {
        h3 {
          font-size: 1.125rem;
          font-weight: 600;
          margin: 0 0 0.25rem 0;
        }

        .date {
          color: #6b7280;
          font-size: 0.875rem;
          margin: 0;
        }
      }
    }

    .ticket-actions {
      .more-options {
        background: none;
        border: none;
        padding: 0.25rem 0.5rem;
        cursor: pointer;
        color: #6b7280;

        &:hover {
          color: #1f2937;
        }
      }
    }
  }

  .ticket-details {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-top: 0.5rem;

    .payment-info {
      p {
        margin: 0 0 0.25rem 0;
        font-size: 0.875rem;
        color: #6b7280;
      }

      .items {
        color: #1f2937;
      }
    }

    .price {
      font-size: 1.125rem;
      font-weight: 600;
      color: #6366f1;
    }
  }

  .ticket-rating {
    margin-top: 0.75rem;
    .stars {
      color: #f59e0b;
      i {
        margin-right: 0.125rem;
      }
    }
  }
}

::-webkit-scrollbar {
  width: 6px;
}

::-webkit-scrollbar-track {
  background: #f3f4f6;
}

::-webkit-scrollbar-thumb {
  background: #9ca3af;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: #6b7280;
}

.receipt-container {
  padding: 2rem;
  max-width: 100%; // Full width for better space usage
  margin: 0 auto;
  margin-left: 250px; // Increased to match sidebar width
  min-height: 100vh;
  width: calc(100% - 250px); // Adjusted to match new margin
  position: relative;
  z-index: 1; // Ensure content stays above background elements
  overflow-x: hidden; // Prevent horizontal overflow
}

.receipt-layout {
  display: flex;
  gap: 1.5rem;
  align-items: flex-start;
  max-width: 1600px; // Increased for wider layout
  margin: 0 auto;
  width: 100%; // Ensure it takes full width of container
  box-sizing: border-box; // Include padding in width calculation
  overflow: visible; // Allow content to be visible
}

.receipt-card {
  flex: 1; // Take maximum available space
  background: white;
  border-radius: 16px;
  padding: 2rem;
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.12);
  border: 1px solid #e5e7eb;
  transition: all 0.3s ease;
  box-sizing: border-box; // Include padding in width calculation
  max-width: 100%; // Prevent overflow
  overflow: hidden; // Hide any overflow content
  z-index: 2; // Ensure it stays above other elements

  &:hover {
    transform: translateY(-4px);
    box-shadow: 0 10px 28px rgba(0, 0, 0, 0.15);
  }
}

.receipt-header {
  text-align: center;
  margin-bottom: 1.5rem;
  padding-bottom: 1rem;
  border-bottom: 1px solid #e5e7eb;

  .merchant-info {
    display: flex;
    flex-direction: column;
    align-items: center;

    .merchant-logo-container {
      margin-bottom: 0.75rem;

      .merchant-logo {
        width: 96px;
        height: 96px;
        object-fit: contain;
        border-radius: 12px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);

        &.unknown-brand {
          background-color: #f3f4f6;
          display: flex;
          align-items: center;
          justify-content: center;

          i {
            font-size: 40px;
            color: #9ca3af;
          }
        }
      }
    }

    h2 {
      font-size: 1.75rem;
      font-weight: 700;
      margin: 0 0 0.5rem;
      color: #1f2937;
    }

    .address, .phone {
      color: #6b7280;
      margin: 0.25rem 0;
      font-size: 0.95rem;
    }
  }

  .rating {
    margin-top: 1rem;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 0.5rem;

    .stars {
      display: flex;
      align-items: center;
      gap: 3px;

      i {
        color: #f59e0b;
        font-size: 1.25rem;
      }
    }

    .rating-value {
      font-weight: 500;
      color: #4b5563;
      font-size: 0.95rem;
    }
  }
}

.receipt-details {
  width: 100%;
  box-sizing: border-box;
  overflow: hidden; // Hide any overflow

  .receipt-meta {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 1rem;
    margin-bottom: 1.5rem;
    color: #6b7280;
    font-size: 0.95rem;
    width: 100%;

    p {
      margin: 0;
    }

    .status-completed {
      color: #10b981;
      font-weight: 600;
    }

    .status-pending {
      color: #f59e0b;
      font-weight: 600;
    }

    .status-cancelled {
      color: #ef4444;
      font-weight: 600;
    }
  }

  .receipt-items {
    margin-bottom: 1.5rem;
    width: 100%;
    overflow-x: unset; // Remove horizontal scrolling
    overflow-y: unset; // Remove vertical scrolling

    table {
      width: 100%;
      border-collapse: separate;
      border-spacing: 0;
      min-width: 650px;

      th, td {
        padding: 1rem;
        text-align: left;
        border-bottom: 1px solid #e5e7eb;
        white-space: normal;
        word-break: break-word;
      }

      th {
        font-weight: 600;
        color: #1f2937;
        font-size: 0.95rem;
        background: #f9fafb;
      }

      td {
        color: #4b5563;
        font-size: 0.95rem;
      }

      .receipt-transaction-row {
        transition: all 0.3s ease;
        height: auto !important; // Ensure row height is automatic
        // No overflow or scrollbars
        overflow: unset !important;
        .brand-logo {
          width: 20px;
          height: 20px;
          object-fit: contain;
          border-radius: 4px;
        }
      }
    }
  }

  .receipt-summary {
    margin: 1.5rem 0;
    padding: 1rem;
    background: #f9fafb;
    border-radius: 8px;

    .summary-row {
      display: flex;
      justify-content: space-between;
      margin: 0.75rem 0;
      font-size: 0.95rem;
      color: #6b7280;

      &.discount {
        color: #ef4444;
      }

      &.total {
        font-weight: 700;
        color: #1f2937;
        font-size: 1.1rem;
        margin-top: 1rem;
      }
    }
  }

  .payment-info {
    display: flex;
    justify-content: space-between;
    margin-top: 1.5rem;
    font-size: 0.95rem;
    flex-wrap: wrap;
    gap: 1rem;

    .payment-method, .articles-count {
      display: flex;
      flex-direction: column;
      gap: 0.5rem;

      span:first-child {
        color: #6b7280;
        font-weight: 500;
      }

      .payment-method-container {
        display: flex;
        align-items: center;
        gap: 0.75rem;
      }

      .payment-label {
        color: #1f2937;
        font-weight: 500;
      }

      .payment-icon {
        color: #a78bfa;
        font-size: 1.25rem;
        transition: transform 0.2s ease, color 0.2s ease;

        &:hover {
          transform: scale(1.15);
          color: #8b5cf6;
        }
      }

      i.fa-cc-visa { color: #1a1f71; }
      i.fa-cc-mastercard { color: #eb001b; }
      i.fa-cc-amex { color: #006fcf; }
      i.fa-paypal { color: #003087; }
      i.fa-money-bill-wave { color: #10b981; }
      i.fa-university { color: #3b82f6; }
      i.fa-truck { color: #f59e0b; }
    }

    .articles-count {
      span:last-child {
        color: #1f2937;
        font-weight: 600;
      }
    }
  }
}

.receipt-sidebar {
  flex: 0 0 400px; // Increased width for better content fit
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
  box-sizing: border-box; // Include padding in width calculation
  max-width: 400px; // Ensure it doesn't exceed its container
  z-index: 2; // Ensure it stays above other elements
}

.sidebar-section {
  background: var(--card-bg, white);
  border-radius: 16px;
  padding: 1.5rem;
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.12);
  border: 1px solid var(--border-color, #e5e7eb);
  transition: all 0.3s ease;
  color: var(--text-color, #333);

  &:hover {
    transform: translateY(-4px);
    box-shadow: 0 10px 28px rgba(0, 0, 0, 0.15);
  }
}

.recommendations {
  .sidebar-title {
    font-size: 1.25rem;
    font-weight: 700;
    color: var(--text-color, #1f2937);
    margin-bottom: 1rem;
    border-bottom: 1px solid var(--border-color, #e5e7eb);
    padding-bottom: 0.5rem;
  }

  .product-recommendations {
    margin-top: 1rem;

    swiper-container {
      width: 100%;
      height: 100%;
      padding: 1rem 0;
    }

    swiper-slide {
      display: flex;
      justify-content: center;
      align-items: center;
      width: 100%;
      height: auto;
      padding: 0.5rem;
    }

    .recommendation-item {
      width: 100%;
      background: var(--card-bg, white);
      border-radius: 12px;
      overflow: hidden;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
      transition: transform 0.3s ease;
      cursor: pointer;
      display: flex;
      flex-direction: column;

      &:hover {
        transform: translateY(-4px);
      }

      .product-image {
        width: 100%;
        height: 200px;
        object-fit: contain;
        background: var(--secondary-bg, #f8f9fa);
        padding: 0.5rem;
      }

      .recommendation-details {
        padding: 1rem;
        background: var(--card-bg, white);

        .product-title {
          font-size: 0.9rem;
          font-weight: 600;
          color: var(--text-color, #333);
          margin-bottom: 0.25rem;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
        }

        .brand-name {
          font-size: 0.8rem;
          color: var(--text-color, #666);
        }
      }
    }
  }

  // Swiper navigation styles
  ::ng-deep {
    .swiper-button-next,
    .swiper-button-prev {
      color: #6B48FF;
      width: 30px;
      height: 30px;
      background: var(--card-bg, white);
      border-radius: 50%;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);

      &::after {
        font-size: 1rem;
      }
    }

    .swiper-pagination-bullet {
      background: #6B48FF;
      opacity: 0.5;

      &-active {
        opacity: 1;
      }
    }
  }
}

.rating-section {
  text-align: center;

  .rating-title {
    font-size: 1.25rem;
    font-weight: 700;
    color: var(--text-color, #1f2937);
    margin-bottom: 0.5rem;
  }

  .brand-highlight {
    color: #6366f1;
  }

  .rating-subtitle {
    font-size: 0.95rem;
    color: var(--text-color, #6b7280);
    margin-bottom: 1rem;
  }

  .rating-stars {
    display: flex;
    justify-content: center;
    gap: 0.5rem;
    margin-bottom: 1rem;

    i {
      font-size: 1.5rem;
      color: #f59e0b;
      cursor: pointer;
      transition: all 0.2s ease;

      &:hover {
        transform: scale(1.1);
      }
    }
  }

  .rating-value {
    font-size: 1.1rem;
    font-weight: 600;
    color: var(--text-color, #1f2937);
    margin: 0.5rem 0;
  }

  .rating-labels {
    display: flex;
    justify-content: space-between;
    margin-bottom: 1.5rem;

    span {
      font-size: 0.9rem;
      color: var(--text-color, #6b7280);
    }
  }

  .confirm-button {
    background-color: #6366f1;
    color: white;
    border: none;
    border-radius: 50px;
    padding: 0.75rem 2rem;
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    width: 100%;

    &:hover {
      background-color: #4f46e5;
      transform: translateY(-2px);
      box-shadow: 0 4px 12px rgba(99, 102, 241, 0.4);
    }
  }
}

.newsletter-section {
  .newsletter-title {
    font-size: 1.25rem;
    font-weight: 700;
    color: var(--text-color, #1f2937);
    margin-bottom: 1rem;
  }

  .newsletter-form {
    margin-bottom: 1.5rem;

    .email-input {
      width: 100%;
      padding: 0.75rem 1.25rem;
      border: 1px solid var(--input-border, #d1d5db);
      border-radius: 50px;
      font-size: 0.95rem;
      margin-bottom: 1rem;
      transition: all 0.3s ease;
      background-color: var(--input-bg, white);
      color: var(--input-text, #333);

      &:focus {
        outline: none;
        border-color: #6366f1;
        box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.15);
      }

      &.previously-used {
        background-color: var(--secondary-bg, #f3f4f6);
        border-color: var(--input-border, #9ca3af);
        color: var(--input-text, #4b5563);

        &:focus {
          background-color: var(--input-bg, white);
          box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.15);
        }
      }
    }

    .subscribe-button {
      background-color: #6366f1;
      color: white;
      border: none;
      border-radius: 50px;
      padding: 0.75rem 2rem;
      font-size: 1rem;
      font-weight: 600;
      cursor: pointer;
      transition: all 0.3s ease;
      width: 100%;

      &:hover {
        background-color: #4f46e5;
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(99, 102, 241, 0.4);
      }
    }
  }

  .social-icons {
    display: flex;
    justify-content: center;
    gap: 0.75rem;

    .social-icon {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 40px;
      height: 40px;
      border-radius: 50%;
      color: white;
      font-size: 1.1rem;
      transition: all 0.3s ease;

      &:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
      }

      &.facebook { background-color: #1877f2; }
      &.instagram { background: linear-gradient(45deg, #f09433 0%, #e6683c 25%, #dc2743 50%, #cc2366 75%, #bc1888 100%); }
      &.linkedin { background-color: #0a66c2; }
      &.youtube { background-color: #ff0000; }
      &.whatsapp { background-color: #25d366; }
      &.messenger { background-color: #00b2ff; }
    }
  }

  .success-message-container {
    text-align: center;
    padding: 1.5rem;
    animation: fadeIn 0.5s ease-in-out;

    .success-icon i {
      font-size: 2.5rem;
      color: #10b981;
      animation: pulse 1.5s ease-in-out;
    }

    .success-title {
      font-size: 1.25rem;
      font-weight: 700;
      color: #10b981;
      margin: 0.5rem 0;
    }

    .success-text {
      font-size: 0.95rem;
      color: var(--text-color, #1f2937);
    }

    @keyframes pulse {
      0% { transform: scale(0.9); opacity: 0.7; }
      50% { transform: scale(1.1); opacity: 1; }
      100% { transform: scale(1); }
    }

    @keyframes fadeIn {
      from { opacity: 0; }
      to { opacity: 1; }
    }
  }
}

.eco-footer {
  text-align: center;
  background: #ecfdf5;
  padding: 1.5rem;
  border-radius: 12px;

  .eco-icons {
    display: flex;
    justify-content: center;
    gap: 1.25rem;
    margin-bottom: 1rem;

    .eco-icon {
      width: 48px;
      height: 48px;
      display: flex;
      align-items: center;
      justify-content: center;
      border-radius: 50%;
      background: white;
      box-shadow: 0 3px 6px rgba(0, 0, 0, 0.1);

      i {
        font-size: 1.5rem;
      }

      &.tree i { color: #15803d; }
      &.leaf i { color: #22c55e; }
      &.seedling i { color: #4ade80; }
    }
  }

  .eco-message {
    font-size: 0.95rem;
    color: #1f2937;
    margin-bottom: 1rem;
    font-weight: 500;
  }

  .eco-details {
    display: flex;
    justify-content: center;
    gap: 1.25rem;
    margin-bottom: 1rem;

    .eco-detail {
      display: flex;
      align-items: center;
      gap: 0.5rem;

      i {
        color: #16a34a;
        font-size: 1rem;
      }

      span {
        font-size: 0.9rem;
        color: #4b5563;
      }
    }
  }

  .company-slogan {
    font-size: 0.85rem;
    color: #6b7280;
    font-style: italic;
  }
}

@media (max-width: 1280px) {
  .receipt-container {
    margin-left: 250px; // Keep the sidebar space
    padding: 1.5rem;
    width: calc(100% - 250px); // Maintain proper width
  }

  .receipt-layout {
    flex-direction: column;
    gap: 1rem;
  }

  .receipt-sidebar {
    flex: 0 0 auto;
    width: 100%;
    max-width: 600px;
    margin: 0 auto;
  }
}

@media (max-width: 768px) {
  .receipt-container {
    padding: 1rem;
    margin-left: 0; // Remove margin on mobile as sidebar collapses
    width: 100%; // Full width on mobile
    overflow-x: hidden; // Prevent horizontal overflow
  }

  .receipt-card {
    padding: 1.25rem;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    width: 100%; // Ensure full width
    max-width: 100%; // Prevent overflow
    box-sizing: border-box; // Include padding in width calculation

    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 6px 16px rgba(0, 0, 0, 0.12);
    }
  }

  .receipt-header {
    .merchant-info {
      .merchant-logo {
        width: 72px;
        height: 72px;
      }

      h2 {
        font-size: 1.5rem;
      }
    }
  }

  .receipt-details {
    .receipt-items {
      overflow-x: auto;

      table {
        font-size: 0.85rem;
      }
    }
  }

  .sidebar-section {
    padding: 1.25rem;
  }

  .recommendations {
    .product-recommendations {
      .recommendation-item {
        .product-image {
          height: 180px;
        }
      }
    }
  }

  .eco-footer {
    .eco-icons {
      gap: 0.75rem;

      .eco-icon {
        width: 40px;
        height: 40px;

        i {
          font-size: 1.25rem;
        }
      }
    }

    .eco-details {
      flex-direction: column;
      gap: 0.5rem;
    }
  }

  .receipt-card-actions {
    display: flex;
    justify-content: space-between;
    gap: 0;
    margin: 0;
    padding: 0 0.25rem;
    width: 100%;

    .refund-btn, .export-btn {
      padding: 0.15rem 0.35rem;
      font-size: 0.65rem;
      min-width: auto;
      margin: 0;

      i {
        font-size: 0.65rem;
      }
    }

    .export-btn::after {
      font-size: 0.45em;
      margin-left: 0.2em;
    }
  }
}

.receipt-card-actions {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;

  .refund-btn, .export-btn {
    border: none;
    outline: none;
    border-radius: 24px;
    padding: 0.5rem 1.5rem;
    font-size: 1rem;
    font-weight: 500;
    box-shadow: 0 2px 8px rgba(99, 102, 241, 0.08);
    transition: all 0.2s;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 0.5rem;
  }

  .refund-btn {
    background: #f5f3ff;
    color: #7c3aed;
    border: 1.5px solid #a78bfa;
    box-shadow: 0 2px 8px rgba(124, 58, 237, 0.08);
  }
  .refund-btn:hover {
    background: #ede9fe;
    color: #5b21b6;
    border-color: #7c3aed;
  }

  .export-btn {
    background: #f3f4f6;
    color: #374151;
    border: 1.5px solid #e5e7eb;
    position: relative;
  }
  .export-btn::after {
    content: '\25BC';
    font-size: 0.8em;
    margin-left: 0.5em;
    color: #a1a1aa;
    display: inline-block;
    vertical-align: middle;
  }
  .export-btn:hover {
    background: #e5e7eb;
    color: #111827;
    border-color: #d1d5db;
  }
}

.receipt-items {
  margin: 20px 0;
  border: 1px solid #eee;
  border-radius: 8px;
  overflow: hidden;

  .product-list {
    // Container for all product rows
    .product-rows {
      display: flex;
      flex-direction: column;
      width: 100%;
      border-bottom: 2px solid #eee;
    }

    .product-row {
      padding: 15px;
      border-bottom: 1px solid #eee;
      background: white;
      transition: background-color 0.2s ease;
      width: 100%;
      box-sizing: border-box;
      display: block; // Ensure each row is a block element

      &:last-child {
        border-bottom: none;
      }

      &:hover {
        background-color: #f8f9fa;
      }

      .product-info {
        display: flex;
        justify-content: space-between;
        align-items: center;
        width: 100%;
        min-height: 30px; // Ensure minimum height for each row

        .product-name {
          font-weight: 500;
          color: #333;
          flex: 1;
          padding-right: 10px;
          overflow: hidden;
          text-overflow: ellipsis;
          display: inline-block; // Ensure proper display
        }

        .price-details {
          display: flex;
          align-items: center;
          gap: 20px;
          min-width: 120px;
          justify-content: flex-end;
          white-space: nowrap;

          .quantity {
            color: #666;
            font-size: 0.9em;
          }

          .price {
            font-weight: 500;
            color: #333;
            min-width: 80px; // Ensure minimum width for price
            text-align: right; // Right-align price
          }
        }
      }
    }

    .totals-section {
      padding: 15px;
      background: #f8f9fa;
      border-top: 2px solid #eee;

      .subtotal-row,
      .vat-row,
      .total-row {
        display: flex;
        justify-content: space-between;
        padding: 8px 0;
        color: #666;

        span:last-child {
          font-weight: 500;
          color: #333;
        }
      }

      .total-row {
        margin-top: 10px;
        padding-top: 10px;
        border-top: 1px solid #eee;
        font-weight: 600;
        color: #333;
      }
    }
  }
}