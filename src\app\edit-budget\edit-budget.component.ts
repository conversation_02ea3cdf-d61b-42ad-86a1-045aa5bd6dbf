import { Component, OnInit, OnDestroy } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { Router, RouterModule, ActivatedRoute } from '@angular/router';
import { TopNavbarComponent } from '../top-navbar/top-navbar.component';
import { SidebarComponent } from '../sidebar/sidebar.component';
import { Budget, BudgetPeriod } from '../models/budget.model';
import { BudgetService } from '../services/budget.service';
import { ThemeService } from '../services/theme.service';
import { Subject } from 'rxjs';
import { takeUntil } from 'rxjs/operators';

@Component({
  selector: 'app-edit-budget',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    RouterModule,
    TopNavbarComponent,
    SidebarComponent
  ],
  templateUrl: './edit-budget.component.html',
  styleUrls: ['./edit-budget.component.scss']
})
export class EditBudgetComponent implements OnInit, OnDestroy {
  // Expose BudgetPeriod enum to the template
  BudgetPeriod = BudgetPeriod;

  budget: Budget = {
    id: '',
    name: '',
    period: BudgetPeriod.OneMonth,
    amount: 0,
    category: '',
    createdAt: new Date(),
    notifications: {
      budgetOverrun: true,
      riskOfOverrun: true
    }
  };

  originalBudget: Budget | null = null;
  availableCategories: string[] = [];
  showNotificationSettings: boolean = false;
  isDarkMode: boolean = false;
  isLoading: boolean = true;
  notFound: boolean = false;
  private destroy$ = new Subject<void>();

  constructor(
    private budgetService: BudgetService,
    private router: Router,
    private route: ActivatedRoute,
    private themeService: ThemeService
  ) {}

  ngOnInit(): void {
    // Subscribe to theme changes
    this.themeService.isDarkMode$
      .pipe(takeUntil(this.destroy$))
      .subscribe(isDark => {
        this.isDarkMode = isDark;
      });

    this.availableCategories = this.budgetService.getAvailableCategories();
    
    // Get budget ID from route params
    this.route.paramMap.subscribe(params => {
      const budgetId = params.get('id');
      if (budgetId) {
        this.loadBudget(budgetId);
      } else {
        this.notFound = true;
        this.isLoading = false;
      }
    });
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  loadBudget(budgetId: string): void {
    const budget = this.budgetService.getBudgetById(budgetId);
    if (budget) {
      // Make a deep copy of the budget to avoid modifying the original directly
      this.originalBudget = JSON.parse(JSON.stringify(budget));
      this.budget = JSON.parse(JSON.stringify(budget));
      this.isLoading = false;
    } else {
      this.notFound = true;
      this.isLoading = false;
    }
  }

  toggleNotificationSettings(): void {
    this.showNotificationSettings = !this.showNotificationSettings;
  }

  updateBudget(): void {
    if (this.validateBudget()) {
      // Update the budget
      this.budgetService.updateBudget(this.budget);
      this.router.navigate(['/budget']);
    }
  }

  cancel(): void {
    this.router.navigate(['/budget']);
  }

  deleteBudget(): void {
    if (confirm('Are you sure you want to delete this budget?')) {
      this.budgetService.deleteBudget(this.budget.id);
      this.router.navigate(['/budget']);
    }
  }

  private validateBudget(): boolean {
    // Basic validation
    if (!this.budget.name || this.budget.name.trim() === '') {
      alert('Please enter a budget name');
      return false;
    }

    if (!this.budget.amount || this.budget.amount <= 0) {
      alert('Please enter a valid budget amount');
      return false;
    }

    if (!this.budget.category || this.budget.category.trim() === '') {
      alert('Please select a category');
      return false;
    }

    return true;
  }
}
