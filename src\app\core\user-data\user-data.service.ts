import { Injectable, signal, computed, inject, effect } from '@angular/core';
import { Observable, of } from 'rxjs';
import { AuthService } from '../auth/auth.service';

// Complete user profile interface with all possible fields
export interface UserProfile {
  // Basic info
  fullName: string;
  email: string;
  birthday: string;
  phoneNumber: string;
  governorate: string;
  gender: string;
  address: string;
  
  // Avatar data
  avatar: string;
  hasCustomAvatar: boolean;
  avatarPositionY: number;
  
  // Occupation data
  role: string;
  occupationType: 'Working' | 'Study' | 'Train' | 'Unemployed' | '';
  occupationPlace: string;
  
  // UI state flags
  showGenderPlaceholder: boolean;
  showAddressPlaceholder: boolean;
  showOccupationPlaceholder: boolean;
  showFullNamePlaceholder: boolean;
  
  // Additional data
  lastShoppingDate: string;
}

@Injectable({
  providedIn: 'root'
})
export class UserDataService {
  private authService = inject(AuthService);
  
  // Private signals for user data
  private _userProfile = signal<UserProfile | null>(null);
  private _isLoading = signal<boolean>(false);
  
  // Public readonly signals
  readonly userProfile = this._userProfile.asReadonly();
  readonly isLoading = this._isLoading.asReadonly();
  
  // Computed signals for easy access to specific fields
  readonly fullName = computed(() => this._userProfile()?.fullName || '');
  readonly email = computed(() => this._userProfile()?.email || '');
  readonly birthday = computed(() => this._userProfile()?.birthday || '');
  readonly phoneNumber = computed(() => this._userProfile()?.phoneNumber || '');
  readonly governorate = computed(() => this._userProfile()?.governorate || '');
  readonly gender = computed(() => this._userProfile()?.gender || '');
  readonly address = computed(() => this._userProfile()?.address || '');
  readonly avatar = computed(() => this._userProfile()?.avatar || 'assets/images/default-avatar.svg');
  readonly hasCustomAvatar = computed(() => this._userProfile()?.hasCustomAvatar || false);
  readonly avatarPositionY = computed(() => this._userProfile()?.avatarPositionY || 0);
  readonly role = computed(() => this._userProfile()?.role || '');
  readonly occupationType = computed(() => this._userProfile()?.occupationType || '');
  readonly occupationPlace = computed(() => this._userProfile()?.occupationPlace || '');
  readonly lastShoppingDate = computed(() => this._userProfile()?.lastShoppingDate || 'Yesterday');
  
  // Placeholder flags
  readonly showGenderPlaceholder = computed(() => this._userProfile()?.showGenderPlaceholder ?? true);
  readonly showAddressPlaceholder = computed(() => this._userProfile()?.showAddressPlaceholder ?? true);
  readonly showOccupationPlaceholder = computed(() => this._userProfile()?.showOccupationPlaceholder ?? true);
  readonly showFullNamePlaceholder = computed(() => this._userProfile()?.showFullNamePlaceholder ?? true);

  constructor() {
    // Watch for auth user changes and load corresponding profile using effect
    effect(() => {
      const authUser = this.authService.currentUser();
      console.log('UserDataService effect triggered - authUser:', authUser);
      if (authUser) {
        this.loadUserProfile(authUser.email);
      } else {
        this._userProfile.set(null);
        console.log('No auth user, profile set to null');
      }
    });
  }

  /**
   * Load user profile data for a specific email
   */
  private loadUserProfile(email: string): void {
    console.log('Loading user profile for email:', email);
    this._isLoading.set(true);

    try {
      const userProfileData = this.getUserProfileFromStorage(email);
      const authUser = this.authService.currentUser();

      console.log('Existing profile data:', userProfileData);
      console.log('Current auth user for merging:', authUser);

      if (userProfileData) {
        // Check if we need to merge data from AuthService (e.g., from sup-info)
        const authBirthday = (authUser as any)?.birthday;
        const authPhoneNumber = (authUser as any)?.phoneNumber;
        const authGender = (authUser as any)?.gender;
        const authAvatar = authUser?.avatar;

        console.log('Checking merge conditions:');
        console.log('- Auth birthday:', authBirthday, 'Profile birthday:', userProfileData.birthday);
        console.log('- Auth phone:', authPhoneNumber, 'Profile phone:', userProfileData.phoneNumber);
        console.log('- Auth gender:', authGender, 'Profile gender:', userProfileData.gender);
        console.log('- Auth avatar:', authAvatar, 'Profile avatar:', userProfileData.avatar);

        // FORCE MERGE for testing - always merge if auth data exists
        const shouldMergeAuthData = authUser && (
          authBirthday || authPhoneNumber || authGender ||
          (authAvatar && authAvatar !== 'assets/images/default-avatar.svg')
        );

        console.log('Should merge auth data (FORCE MODE):', shouldMergeAuthData);

        if (shouldMergeAuthData) {
          // Merge auth data into existing profile
          const mergedProfile: UserProfile = {
            ...userProfileData,
            birthday: (authUser as any)?.birthday || userProfileData.birthday,
            phoneNumber: (authUser as any)?.phoneNumber || userProfileData.phoneNumber,
            gender: this.capitalizeGender((authUser as any)?.gender) || userProfileData.gender,
            avatar: authUser?.avatar || userProfileData.avatar,
            hasCustomAvatar: authUser?.hasCustomAvatar || userProfileData.hasCustomAvatar,
            avatarPositionY: authUser?.avatarPositionY || userProfileData.avatarPositionY,
            showGenderPlaceholder: !(authUser as any)?.gender && userProfileData.showGenderPlaceholder
          };

          this._userProfile.set(mergedProfile);
          this.saveUserProfileToStorage(email, mergedProfile);
          console.log('User profile merged with auth data for:', email, mergedProfile);
        } else {
          this._userProfile.set(userProfileData);
          console.log('User profile loaded for:', email, userProfileData);
        }
      } else {
        // Create default profile for new user
        const defaultProfile = this.createDefaultProfile(email);
        this._userProfile.set(defaultProfile);
        this.saveUserProfileToStorage(email, defaultProfile);
        console.log('Default profile created for new user:', email);
      }
    } catch (error) {
      console.error('Error loading user profile:', error);
      // Fallback to default profile
      const defaultProfile = this.createDefaultProfile(email);
      this._userProfile.set(defaultProfile);
    } finally {
      this._isLoading.set(false);
    }
  }

  /**
   * Create default profile for a new user
   */
  private createDefaultProfile(email: string): UserProfile {
    // Get basic info from AuthService if available
    const authUser = this.authService.currentUser();

    return {
      fullName: authUser?.full_name || '',
      email: email,
      birthday: (authUser as any)?.birthday || '',
      phoneNumber: (authUser as any)?.phoneNumber || '+216 00 000 000',
      governorate: 'Tunisie',
      gender: this.capitalizeGender((authUser as any)?.gender) || '',
      address: (authUser as any)?.address || '',
      avatar: authUser?.avatar || 'assets/images/default-avatar.svg',
      hasCustomAvatar: authUser?.hasCustomAvatar || false,
      avatarPositionY: authUser?.avatarPositionY || 0,
      role: (authUser as any)?.role || '',
      occupationType: (authUser as any)?.occupationType || '',
      occupationPlace: (authUser as any)?.occupationPlace || '',
      showGenderPlaceholder: !(authUser as any)?.gender,
      showAddressPlaceholder: !(authUser as any)?.address,
      showOccupationPlaceholder: !(authUser as any)?.occupationType,
      showFullNamePlaceholder: !authUser?.full_name,
      lastShoppingDate: 'Yesterday'
    };
  }

  /**
   * Get user profile from localStorage
   */
  private getUserProfileFromStorage(email: string): UserProfile | null {
    try {
      const storageKey = `userProfile_${email}`;
      const storedProfile = localStorage.getItem(storageKey);
      
      if (storedProfile) {
        return JSON.parse(storedProfile) as UserProfile;
      }
      
      return null;
    } catch (error) {
      console.error('Error loading user profile from storage:', error);
      return null;
    }
  }

  /**
   * Save user profile to localStorage
   */
  private saveUserProfileToStorage(email: string, profile: UserProfile): void {
    try {
      const storageKey = `userProfile_${email}`;
      localStorage.setItem(storageKey, JSON.stringify(profile));
      console.log('User profile saved for:', email);
    } catch (error) {
      console.error('Error saving user profile to storage:', error);
    }
  }

  /**
   * Update user profile field
   */
  updateField<K extends keyof UserProfile>(field: K, value: UserProfile[K]): Observable<UserProfile> {
    const currentProfile = this._userProfile();
    const currentUser = this.authService.currentUser();
    
    if (!currentProfile || !currentUser) {
      return of(null as any);
    }

    const updatedProfile: UserProfile = {
      ...currentProfile,
      [field]: value
    };

    // Update signal
    this._userProfile.set(updatedProfile);
    
    // Save to localStorage
    this.saveUserProfileToStorage(currentUser.email, updatedProfile);
    
    // Also update AuthService if it's a field that exists there
    if (field === 'fullName') {
      this.authService.updateUser({ full_name: value as string });
    } else if (field === 'email') {
      this.authService.updateUser({ email: value as string });
    } else if (field === 'avatar' || field === 'hasCustomAvatar' || field === 'avatarPositionY') {
      this.authService.updateUser({
        [field]: value,
        avatar: updatedProfile.avatar,
        hasCustomAvatar: updatedProfile.hasCustomAvatar,
        avatarPositionY: updatedProfile.avatarPositionY
      } as any);
    } else if (field === 'address' || field === 'occupationType' || field === 'occupationPlace' || field === 'role') {
      // Save address and occupation data to AuthService for persistence
      this.authService.updateUser({
        [field]: value,
        address: updatedProfile.address,
        occupationType: updatedProfile.occupationType,
        occupationPlace: updatedProfile.occupationPlace,
        role: updatedProfile.role
      } as any);
    }

    console.log(`Updated ${field} for user ${currentUser.email}:`, value);
    return of(updatedProfile);
  }

  /**
   * Update multiple fields at once
   */
  updateProfile(updates: Partial<UserProfile>): Observable<UserProfile> {
    const currentProfile = this._userProfile();
    const currentUser = this.authService.currentUser();
    
    if (!currentProfile || !currentUser) {
      return of(null as any);
    }

    const updatedProfile: UserProfile = {
      ...currentProfile,
      ...updates
    };

    // Update signal
    this._userProfile.set(updatedProfile);
    
    // Save to localStorage
    this.saveUserProfileToStorage(currentUser.email, updatedProfile);
    
    // Update AuthService with relevant fields
    const authUpdates: any = {};
    if (updates.fullName) authUpdates.full_name = updates.fullName;
    if (updates.email) authUpdates.email = updates.email;
    if (updates.avatar) authUpdates.avatar = updates.avatar;
    if (updates.hasCustomAvatar !== undefined) authUpdates.hasCustomAvatar = updates.hasCustomAvatar;
    if (updates.avatarPositionY !== undefined) authUpdates.avatarPositionY = updates.avatarPositionY;
    if (updates.address !== undefined) authUpdates.address = updates.address;
    if (updates.occupationType !== undefined) authUpdates.occupationType = updates.occupationType;
    if (updates.occupationPlace !== undefined) authUpdates.occupationPlace = updates.occupationPlace;
    if (updates.role !== undefined) authUpdates.role = updates.role;
    
    if (Object.keys(authUpdates).length > 0) {
      this.authService.updateUser(authUpdates);
    }

    console.log(`Updated profile for user ${currentUser.email}:`, updates);
    return of(updatedProfile);
  }

  /**
   * Clear user data (for logout)
   */
  clearUserData(): void {
    this._userProfile.set(null);
    console.log('User data cleared');
  }

  /**
   * Force reload user profile from storage
   */
  reloadProfile(): void {
    const currentUser = this.authService.currentUser();
    if (currentUser) {
      this.loadUserProfile(currentUser.email);
    }
  }

  /**
   * Helper method to capitalize gender values from sup-info
   */
  private capitalizeGender(gender: string): string {
    if (!gender) return '';

    switch (gender.toLowerCase()) {
      case 'male':
        return 'Male';
      case 'female':
        return 'Female';
      case 'other':
        return 'Other';
      default:
        return gender;
    }
  }
}
