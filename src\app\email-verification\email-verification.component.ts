import { Component, EventEmitter, Input, Output } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { RouterModule, Router } from '@angular/router';
import { Location } from '@angular/common';

@Component({
  selector: 'app-email-verification',
  standalone: true,
  imports: [CommonModule, FormsModule, RouterModule],
  templateUrl: './email-verification.component.html',
  styleUrls: ['./email-verification.component.css']
})
export class EmailVerificationComponent {
  @Input() email: string = '';
  @Output() emailVerified = new EventEmitter<void>();
  @Output() skipped = new EventEmitter<void>();
  verificationCode: string = '';
  errorMessage: string = '';
  isResending: boolean = false;
  countdown: number = 0;

  constructor(private location: Location, private router: Router) {}

  goBack() {
    this.location.back();
  }

  skip() {
    console.log('Skipping to personal information page');
    this.skipped.emit();
  }

  verifyCode() {
    if (this.verificationCode === '123456') {
      this.emailVerified.emit();
    } else {
      this.errorMessage = 'Invalid verification code. Please try again.';
    }
  }

  resendCode() {
    if (this.countdown > 0) return;
    
    this.isResending = true;
    this.errorMessage = '';
    
    // Simulate sending a new verification email
    console.log('Resending verification email to:', this.email);
    
    setTimeout(() => {
      this.isResending = false;
      this.countdown = 30;
      const timer = setInterval(() => {
        this.countdown--;
        if (this.countdown <= 0) {
          clearInterval(timer);
        }
      }, 1000);
    }, 1000);
  }
}