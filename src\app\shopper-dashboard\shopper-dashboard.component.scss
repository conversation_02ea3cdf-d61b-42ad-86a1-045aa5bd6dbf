/* Component Scoped Reset */
:host {
  display: block;
  box-sizing: border-box;
}

/* Design Tokens */
:host {
  --primary-color: #A78BFA;
  --accent-color: #6366F1;
  --success-color: #10B981;
  --text-primary: var(--text-color, #111827);
  --text-secondary: var(--text-color, #6B7280);
  --card-background: var(--card-bg, #FFFFFF);
  --background-color: var(--primary-bg, #F9FAFB);
  --shadow-sm: 0 2px 6px rgba(0, 0, 0, 0.05);
  --shadow-md: 0 4px 12px rgba(0, 0, 0, 0.08);
  --border-radius-sm: 8px;
  --border-radius-md: 12px;
  --border-radius-lg: 16px;
  --spacing-xs: 4px;
  --spacing-sm: 8px;
  --spacing-md: 16px;
  --spacing-lg: 24px;
  --spacing-xl: 32px;
  --sidebar-width: 280px;
  --card-min-height: 180px;
  --transition: all 0.2s ease;
  --font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
  --font-size-xs: 12px;
  --font-size-sm: 14px;
  --font-size-base: 16px;
  --font-size-lg: 18px;
  --font-size-xl: 22px;
  --border-color-light: var(--border-color, #F1F5F9);
  --hover-bg-light: var(--hover-bg, #F3F4F6);
}

/* Dashboard Container */
.dashboard-container {
  font-family: var(--font-family);
  background: var(--background-color);
  min-height: 100vh;
  padding: var(--spacing-xl);
  transition: var(--transition);

  /* Main Content Grid */
  .main-content {
    max-width: 100%;
    margin: 0 auto;
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: var(--spacing-lg);
    margin-left: var(--sidebar-width);
    padding-right: var(--spacing-xl);

    /* Base Card Styles */
    .card {
      background: var(--card-background);
      border-radius: var(--border-radius-lg);
      box-shadow: var(--shadow-sm);
      padding: var(--spacing-md);
      transition: var(--transition);
      min-height: var(--card-min-height);

      &:hover {
        box-shadow: var(--shadow-md);
      }

      .card-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: var(--spacing-sm);

        h2 {
          font-size: var(--font-size-base);
          font-weight: 600;
          color: var(--text-primary);
          line-height: 1.3;
        }

        .menu-dots {
          font-size: var(--font-size-lg);
          color: var(--text-secondary);
          cursor: pointer;
          padding: var(--spacing-xs);
          border-radius: var(--border-radius-sm);
          transition: var(--transition);

          &:hover {
            background: var(--hover-bg-light);
          }
        }
      }

      /* View All Button */
      .view-all {
        background: var(--secondary-bg, #F3F4FF);
        color: var(--accent-color);
        border: none;
        border-radius: var(--border-radius-md);
        padding: var(--spacing-sm) var(--spacing-md);
        font-size: var(--font-size-sm);
        font-weight: 500;
        cursor: pointer;
        transition: var(--transition);

        &:hover {
          background: var(--hover-bg, #E8E8FF);
          box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }
      }
    }

    /* Lifetime Expenses Card */
    .lifetime-expenses {
      grid-column: 1 / 3;
      grid-row: 1;
      min-height: var(--card-min-height);
      width: 100%;

      .card-header h2 {
        font-size: var(--font-size-xl);
      }

      .amount {
        font-size: 2rem;
        font-weight: 700;
        color: var(--primary-color);
        margin: var(--spacing-sm) 0;
        line-height: 1.2;
      }

      .meta-info {
        display: flex;
        justify-content: space-between;
        margin-top: var(--spacing-md);
        padding-top: var(--spacing-sm);
        border-top: 1px solid var(--border-color-light);
        color: var(--text-secondary);
        font-size: var(--font-size-sm);
        font-weight: 500;

        .articles,
        .time {
          display: flex;
          align-items: center;
          gap: var(--spacing-sm);
        }
      }
    }

    /* Current Month Expenses Card */
    .current-month {
      grid-column: 1 / 3;
      grid-row: 2;
      min-height: var(--card-min-height);
      width: 100%;

      .card-header h2 {
        font-size: var(--font-size-xl);
      }

      .amount {
        font-size: 1.75rem;
        font-weight: 700;
        color: var(--primary-color);
        margin: var(--spacing-sm) 0;
        line-height: 1.2;
      }

      .meta-info {
        display: flex;
        justify-content: space-between;
        margin-top: var(--spacing-md);
        padding-top: var(--spacing-sm);
        border-top: 1px solid var(--border-color-light);
        color: var(--text-secondary);
        font-size: var(--font-size-sm);
        font-weight: 500;

        .articles,
        .time {
          display: flex;
          align-items: center;
          gap: var(--spacing-sm);
        }
      }
    }

    /* Expenses Category Card */
    .expenses-category {
      grid-column: 3 / 5;
      grid-row: 1 / 3;
      width: 100%;
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: var(--spacing-md);

      .card-header {
        grid-column: 1 / -1;

        .title-wrapper {
          display: flex;
          align-items: center;
          gap: var(--spacing-sm);

          .subtitle {
            font-size: var(--font-size-xs);
            color: var(--text-secondary);
            font-weight: 400;
          }
        }

        .dropdown-selector {
          background: var(--secondary-bg, #F9FAFB);
          border: 1px solid var(--border-color, #E5E7EB);
          border-radius: var(--border-radius-sm);
          padding: var(--spacing-xs) var(--spacing-md);
          font-size: var(--font-size-sm);
          font-weight: 500;
          color: var(--text-primary);
          cursor: pointer;
          transition: var(--transition);

          &:hover {
            background: var(--hover-bg-light);
          }

          &:after {
            content: '▼';
            font-size: var(--font-size-xs);
            margin-left: var(--spacing-sm);
            color: var(--text-secondary);
          }
        }
      }

      .chart-container {
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        height: 300px;
        position: relative;
        grid-column: 1;
        aspect-ratio: 1;

        canvas {
          max-height: 250px;
          max-width: 250px;
          width: 100% !important;
          height: 100% !important;
          aspect-ratio: 1;
          object-fit: contain;
        }

        .chart-center {
          position: absolute;
          top: 50%;
          left: 50%;
          transform: translate(-50%, -50%);
          text-align: center;

          .view-details {
            color: var(--text-secondary);
            font-size: var(--font-size-xs);
            font-weight: 500;
            text-decoration: none;
            transition: var(--transition);

            &:hover {
              color: var(--accent-color);
              text-decoration: underline;
            }
          }
        }
      }

      .chart-legend {
        display: flex;
        flex-direction: column;
        gap: var(--spacing-sm);
        padding: var(--spacing-md) 0;
        font-size: var(--font-size-sm);
        font-weight: 500;
        grid-column: 2;
        align-self: center;
        justify-self: start;

        .legend-item {
          display: flex;
          align-items: center;
          gap: var(--spacing-sm);

          .color-indicator {
            width: 12px;
            height: 12px;
            border-radius: var(--border-radius-sm);
            border: 1px solid rgba(0, 0, 0, 0.1);

            &.food {
              background: #A78BFA;
            }

            &.clothes {
              background: #F9A8D4;
            }

            &.leisure {
              background: #60A5FA;
            }

            &.medicines {
              background: #FBBF24;
            }
          }
        }
      }
    }

    /* Expenses Per Month Card */
    .expenses-month {
      grid-column: 1 / 3;
      grid-row: 3;
      width: 100%;

      .card-header {
        margin-bottom: var(--spacing-lg);
      }

      .amount {
        font-size: 1.5rem;
        font-weight: 700;
        color: var(--accent-color);
        margin-bottom: var(--spacing-sm);
      }

      .chart-container {
        height: 180px;
        width: 100%;
        position: relative;
        padding-bottom: 40%;

        canvas {
          position: absolute;
          top: 0;
          left: 0;
          width: 100% !important;
          height: 100% !important;
        }
      }
    }

    /* Saving Plan Card */
    .saving-plan {
      grid-column: 3 / 5;
      grid-row: 3;
      width: 100%;
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: var(--spacing-md);

      .card-header {
        grid-column: 1 / -1;
      }

      .chart-container {
        height: 250px;
        position: relative;

        canvas {
          max-height: 200px;
          max-width: 200px;
          width: 100% !important;
          height: 100% !important;
          aspect-ratio: 1;
          object-fit: contain;
        }

        .chart-center {
          position: absolute;
          top: 50%;
          left: 50%;
          transform: translate(-50%, -50%);
          text-align: center;

          .saving-label {
            font-size: var(--font-size-sm);
            font-weight: 600;
            color: var(--text-primary);
          }

          .saving-sublabel {
            font-size: var(--font-size-xs);
            color: var(--text-secondary);
          }
        }
      }

      .chart-legend {
        display: flex;
        flex-direction: column;
        gap: var(--spacing-sm);
        padding: var(--spacing-md) 0;
        font-size: var(--font-size-sm);
        font-weight: 500;
        grid-column: 2;
        align-self: center;
        justify-self: start;
        margin-top: calc(var(--spacing-xl) * 2);

        .legend-item {
          display: flex;
          align-items: center;
          gap: var(--spacing-sm);

          .color-indicator {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            border: 1px solid rgba(0, 0, 0, 0.1);

            &.product1 {
              background: #FBBF24;
            }

            &.product2 {
              background: #F9A8D4;
            }

            &.product3 {
              background: #60A5FA;
            }

            &.product4 {
              background: #2ECC71;
            }
          }
        }
      }
    }

    /* Transactions Card */
    .transactions {
      grid-column: 1 / -1;
      grid-row: 4;
      width: 100%;

      .transaction-list {
        margin-top: var(--spacing-sm);

        .transaction-item {
          display: flex;
          justify-content: space-between;
          align-items: center;
          padding: var(--spacing-sm) 0;
          border-bottom: 1px solid var(--border-color-light);
          transition: var(--transition);

          &:last-child {
            border-bottom: none;
          }

          .transaction-left {
            display: flex;
            align-items: center;
            gap: var(--spacing-md);

            .indicator {
              width: 8px;
              height: 8px;
              background: var(--primary-color);
              border-radius: 2px;
            }

            .transaction-date {
              font-size: var(--font-size-sm);
              color: var(--text-secondary);
              font-weight: 400;
            }
          }

          .amount {
            font-size: var(--font-size-sm);
            font-weight: 600;
            color: var(--success-color);
          }
        }
      }
    }
  }
}

/* Responsive Design */
@media (max-width: 1024px) {
  .dashboard-container {
    .main-content {
      grid-template-columns: repeat(2, 1fr);
      margin-left: calc(var(--sidebar-width) * 0.8);
      padding-right: var(--spacing-lg);

      .lifetime-expenses {
        grid-column: 1;
        grid-row: 1;
      }

      .current-month {
        grid-column: 2;
        grid-row: 1;
      }

      .expenses-category {
        grid-column: 1 / -1;
        grid-row: 2;
      }

      .expenses-month {
        grid-column: 1 / -1;
        grid-row: 3;
      }

      .saving-plan {
        grid-column: 1 / -1;
        grid-row: 4;
      }

      .transactions {
        grid-column: 1 / -1;
        grid-row: 5;
      }

      .expenses-category {
        .chart-container {
          height: 350px;

          canvas {
            max-height: 300px;
            max-width: 300px;
          }
        }
      }

      .saving-plan {
        .chart-container {
          height: 200px;

          canvas {
            max-height: 180px;
            max-width: 180px;
          }
        }
      }
    }
  }
}

@media (max-width: 768px) {
  .dashboard-container {
    padding: var(--spacing-md);

    .main-content {
      grid-template-columns: 1fr;
      margin-left: 0;
      padding-right: var(--spacing-md);
      gap: var(--spacing-md);

      .card {
        padding: var(--spacing-md);
      }

      .lifetime-expenses {
        grid-column: 1;
        grid-row: 1;
      }

      .current-month {
        grid-column: 1;
        grid-row: 2;
      }

      .expenses-category {
        grid-column: 1;
        grid-row: 3;
      }

      .expenses-month {
        grid-column: 1;
        grid-row: 4;
      }

      .saving-plan {
        grid-column: 1;
        grid-row: 5;
      }

      .transactions {
        grid-column: 1;
        grid-row: 6;
      }

      .expenses-category,
      .saving-plan {
        grid-template-columns: 1fr;

        .chart-container {
          grid-column: 1;
        }

        .chart-legend {
          grid-column: 1;
          flex-direction: row;
          flex-wrap: wrap;
          justify-content: center;
          padding: var(--spacing-md) 0;
        }
      }

      .expenses-category {
        .chart-container {
          height: 250px;

          canvas {
            max-height: 200px;
            max-width: 200px;
          }
        }
      }

      .saving-plan {
        .chart-legend {
          margin-top: calc(var(--spacing-lg) * 2);
        }
      }

      .expenses-month {
        .chart-container {
          height: 160px;
          padding-bottom: 35%;
        }
      }

      .saving-plan {
        .chart-container {
          height: 180px;

          canvas {
            max-height: 150px;
            max-width: 150px;
          }
        }
      }
    }
  }
}

@media (max-width: 480px) {
  .dashboard-container {
    padding: var(--spacing-sm);

    .main-content {
      padding-right: var(--spacing-sm);
      gap: var(--spacing-sm);

      .card {
        padding: var(--spacing-md);
        min-height: auto;

        .card-header h2 {
          font-size: var(--font-size-base);
        }
      }

      .amount {
        font-size: 1.5rem;
      }

      .meta-info {
        flex-direction: column;
        gap: var(--spacing-sm);
      }

      .expenses-category,
      .saving-plan {
        .chart-container {
          height: 140px;
          padding-bottom: 30%;
        }
      }

      .expenses-category,
      .saving-plan {
        .chart-legend {
          flex-direction: row;
          flex-wrap: wrap;
          justify-content: center;
          gap: var(--spacing-md);
        }
      }

      .saving-plan {
        .chart-legend {
          margin-top: calc(var(--spacing-md) * 2);
        }
      }
    }
  }
}