import { Component, OnInit, OnD<PERSON>roy, computed, effect } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule } from '@angular/router';
import { AccountNotificationService } from '../core/account-notifications/account-notification.service';
import { AccountNotification } from '../interfaces/account-notification';
import { Subject } from 'rxjs';
import { takeUntil } from 'rxjs/operators';

@Component({
  selector: 'app-account-notification-content',
  standalone: true,
  imports: [CommonModule, RouterModule],
  templateUrl: './account-notification-content.component.html',
  styleUrl: './account-notification-content.component.scss'
})
export class AccountNotificationContentComponent implements OnInit, OnDestroy {
  private destroy$ = new Subject<void>();

  // Expose signals to template - initialized in constructor
  notifications!: any;
  selectedNotification!: any;
  unreadCount!: any;
  hasUnreadUrgent!: any;
  displayNotifications!: any;

  constructor(
    private accountNotificationService: AccountNotificationService
  ) {
    // Initialize signals after service injection
    this.notifications = this.accountNotificationService.filteredNotifications;
    this.selectedNotification = this.accountNotificationService.selectedNotification;
    this.unreadCount = this.accountNotificationService.unreadCount;
    this.hasUnreadUrgent = this.accountNotificationService.hasUnreadUrgent;

    // Computed for display
    this.displayNotifications = computed(() => {
      const notifications = this.notifications();
      // Sort by timestamp (newest first) and priority
      return notifications.sort((a: AccountNotification, b: AccountNotification) => {
        // First sort by priority (urgent > high > medium > low)
        const priorityOrder: Record<string, number> = { urgent: 4, high: 3, medium: 2, low: 1 };
        const priorityDiff = (priorityOrder[b.priority] || 0) - (priorityOrder[a.priority] || 0);
        if (priorityDiff !== 0) return priorityDiff;

        // Then by timestamp (newest first)
        return new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime();
      });
    });
  }

  ngOnInit(): void {
    // Any initialization logic
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  selectNotification(notification: AccountNotification): void {
    this.accountNotificationService.selectNotification(notification);
  }

  markAsRead(notification: AccountNotification, event: Event): void {
    event.stopPropagation();
    this.accountNotificationService.markAsRead(notification.id);
  }

  deleteNotification(notification: AccountNotification, event: Event): void {
    event.stopPropagation();
    this.accountNotificationService.deleteNotification(notification.id);
  }

  markAllAsRead(): void {
    this.accountNotificationService.markAllAsRead();
  }

  goBack(): void {
    this.accountNotificationService.clearSelection();
  }

  getNotificationIcon(type: AccountNotification['type']): string {
    switch (type) {
      case 'success': return 'fas fa-check-circle';
      case 'error': return 'fas fa-exclamation-circle';
      case 'warning': return 'fas fa-exclamation-triangle';
      case 'info': return 'fas fa-info-circle';
      case 'promotion': return 'fas fa-gift';
      case 'system': return 'fas fa-cog';
      default: return 'fas fa-bell';
    }
  }

  getNotificationColor(type: AccountNotification['type']): string {
    switch (type) {
      case 'success': return '#10b981';
      case 'error': return '#ef4444';
      case 'warning': return '#f59e0b';
      case 'info': return '#3b82f6';
      case 'promotion': return '#8b5cf6';
      case 'system': return '#6b7280';
      default: return '#6b7280';
    }
  }

  getPriorityClass(priority: AccountNotification['priority']): string {
    switch (priority) {
      case 'urgent': return 'priority-urgent';
      case 'high': return 'priority-high';
      case 'medium': return 'priority-medium';
      case 'low': return 'priority-low';
      default: return 'priority-medium';
    }
  }

  formatTimestamp(timestamp: Date): string {
    const now = new Date();
    const diff = now.getTime() - new Date(timestamp).getTime();
    const minutes = Math.floor(diff / 60000);
    const hours = Math.floor(diff / 3600000);
    const days = Math.floor(diff / ********);

    if (minutes < 1) return 'Just now';
    if (minutes < 60) return `${minutes}m ago`;
    if (hours < 24) return `${hours}h ago`;
    if (days < 7) return `${days}d ago`;

    return new Date(timestamp).toLocaleDateString();
  }
}
