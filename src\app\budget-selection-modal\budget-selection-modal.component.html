<div class="modal-overlay" [class.dark-mode]="isDarkMode">
  <div class="modal-content" [class.dark-mode]="isDarkMode">
    <div class="modal-header">
      <h2>Add to Budget</h2>
      <button class="close-btn" (click)="onClose()">
        <i class="fas fa-times"></i>
      </button>
    </div>
    <div class="modal-body">
      <div class="amount-info">
        <p>Total amount to add: <strong>{{ totalAmount }} TND</strong></p>
      </div>

      <!-- Warning message when no budgets are available -->
      <div class="no-budgets-warning" *ngIf="budgets.length === 0">
        <p><i class="fas fa-exclamation-triangle"></i> You don't have any budgets created yet. Create a budget first to track your expenses.</p>
        <button class="create-budget-btn" (click)="navigateToBudgetCreation()">
          <i class="fas fa-plus"></i> Create Budget
        </button>
      </div>

      <!-- Budget selection when budgets are available -->
      <ng-container *ngIf="budgets.length > 0">
        <div class="budget-selection">
          <label>Select Budget:</label>
          <select [(ngModel)]="selectedBudgetId" class="budget-select">
            <option value="">-- Select a budget --</option>
            <option *ngFor="let budget of budgets" [value]="budget.id">
              {{ budget.name }} ({{ budget.remaining }} TND remaining)
            </option>
          </select>
        </div>

        <div class="budget-details" *ngIf="selectedBudgetId">
          <div *ngFor="let budget of budgets">
            <div *ngIf="budget.id === selectedBudgetId" class="selected-budget-info">
              <p><strong>Budget:</strong> {{ budget.name }}</p>
              <p><strong>Category:</strong> {{ budget.category }}</p>
              <p><strong>Current Amount:</strong> {{ budget.amount }} TND</p>
              <p><strong>Spent:</strong> {{ budget.spent }} TND</p>
              <p><strong>Remaining:</strong> {{ budget.remaining }} TND</p>
              <div class="progress-bar">
                <div class="progress-fill" [style.width.%]="budget.percentSpent || 0"></div>
              </div>
              <p class="warning" *ngIf="totalAmount > (budget.remaining || 0)">
                <i class="fas fa-exclamation-triangle"></i>
                Warning: This will exceed your budget by {{ totalAmount - (budget.remaining || 0) }} TND
              </p>
            </div>
          </div>
        </div>
      </ng-container>
    </div>
    <div class="modal-footer">
      <button class="cancel-btn" (click)="onClose()">
        <i class="fas fa-times"></i> Cancel
      </button>
      <button class="add-btn" [disabled]="!selectedBudgetId || budgets.length === 0" (click)="onSelectBudget()">
        <i class="fas fa-plus"></i> Add to Budget
      </button>
    </div>
  </div>
</div>
