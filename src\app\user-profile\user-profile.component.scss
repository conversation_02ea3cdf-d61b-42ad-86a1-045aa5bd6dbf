// Variables
$primary-color: #6b48ff;
$secondary-color: #f8f9fa;
$text-primary: #333;
$text-secondary: #666;
$transition: all 0.3s ease;

// Profile Container
.profile-container {
  padding: 20px;
  margin-left: 270px;
  margin-top: 70px;
  display: flex;
  justify-content: center;
  min-height: calc(100vh - 90px);
  background-color: var(--bg-color, #f8f9fa);
  position: relative; // Ensure proper positioning context
  flex-direction: column; // Stack children vertically
  align-items: center; // Center children horizontally

  @media (max-width: 768px) {
    margin-left: 0;
    padding: 15px;
    padding-bottom: 100px; // Add extra padding at the bottom for notification panel
  }
}

.profile-card {
  background-color: white;
  border-radius: 16px;
  width: 100%;
  max-width: 800px;
  overflow: hidden;
  transition: all 0.3s ease;
  position: relative;
  border: 1px solid rgba(171, 85, 247, 0.2);
  box-shadow:
    0 10px 30px rgba(107, 72, 255, 0.15),
    0 6px 15px rgba(168, 85, 247, 0.1),
    0 0 0 1px rgba(171, 85, 247, 0.05),
    inset 0 0 0 1px rgba(255, 255, 255, 0.3);
  animation: profileCardGlow 1.5s ease-in-out infinite alternate;
  margin-bottom: 30px; // Add space for notification panel

  &::before {
    content: '';
    position: absolute;
    top: -2px;
    left: -2px;
    right: -2px;
    bottom: -2px;
    background: linear-gradient(135deg, rgba(107, 72, 255, 0.4), rgba(168, 85, 247, 0.4), rgba(183, 148, 244, 0.4));
    border-radius: 18px;
    z-index: -1;
    opacity: 0.5;
    filter: blur(8px);
  }

  &:hover {
    transform: translateY(-2px);
    box-shadow:
      0 15px 40px rgba(107, 72, 255, 0.2),
      0 8px 20px rgba(168, 85, 247, 0.15),
      0 0 0 1px rgba(171, 85, 247, 0.1),
      inset 0 0 0 1px rgba(255, 255, 255, 0.4);
  }

  @media (max-width: 768px) {
    margin-bottom: 50px; // Increase space for notification panel in mobile
  }
}

@keyframes profileCardGlow {
  from {
    box-shadow:
      0 10px 30px rgba(107, 72, 255, 0.15),
      0 6px 15px rgba(168, 85, 247, 0.1),
      0 0 0 1px rgba(171, 85, 247, 0.05),
      inset 0 0 0 1px rgba(255, 255, 255, 0.3);
  }
  to {
    box-shadow:
      0 15px 40px rgba(107, 72, 255, 0.2),
      0 8px 20px rgba(168, 85, 247, 0.15),
      0 0 0 1px rgba(171, 85, 247, 0.1),
      inset 0 0 0 1px rgba(255, 255, 255, 0.4);
  }
}

.profile-header {
  display: flex;
  padding: 24px;
  background: linear-gradient(135deg, #f8f9fa, #f1f3f5);
  border-bottom: 1px solid #eee;

  @media (max-width: 576px) {
    flex-direction: column;
    align-items: center;
    text-align: center;
  }
}

.profile-image-container {
  position: relative;
  margin-right: 24px;

  @media (max-width: 576px) {
    margin-right: 0;
    margin-bottom: 16px;
  }

  &:hover .avatar-actions {
    opacity: 1;
  }
}

.avatar-actions {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background-color: rgba(0, 0, 0, 0.3);
  border-radius: 50%;
  opacity: 0;
  transition: opacity 0.3s ease;

  .avatar-edit-container {
    position: relative;

    &:hover .avatar-dropdown,
    .avatar-dropdown:hover {
      opacity: 1;
      visibility: visible;
      transform: translateY(0);
    }
  }

  .avatar-dropdown {
    position: absolute;
    top: 45px;
    left: 50%;
    transform: translateX(-50%) translateY(-10px);
    background: linear-gradient(to bottom, #ffffff, #f9f7ff);
    border-radius: 12px;
    box-shadow:
      0 10px 25px rgba(107, 72, 255, 0.15),
      0 6px 12px rgba(168, 85, 247, 0.1),
      0 0 0 1px rgba(171, 85, 247, 0.05),
      inset 0 0 0 1px rgba(255, 255, 255, 0.7);
    padding: 8px 0;
    min-width: 200px;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s cubic-bezier(0.25, 1, 0.5, 1);
    z-index: 100;
    backdrop-filter: blur(5px);
    border: 1px solid rgba(255, 255, 255, 0.18);
    overflow: hidden;

    &:before {
      content: '';
      position: absolute;
      top: -6px;
      left: 50%;
      width: 12px;
      height: 12px;
      background-color: #ffffff;
      transform: translateX(-50%) rotate(45deg);
      box-shadow:
        -2px -2px 5px rgba(107, 72, 255, 0.05),
        -1px -1px 2px rgba(168, 85, 247, 0.05);
    }

    &:after {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      height: 1px;
      background: linear-gradient(90deg, transparent, rgba(168, 85, 247, 0.2), transparent);
    }
  }

  .avatar-dropdown-item {
    display: flex;
    align-items: center;
    padding: 12px 16px;
    margin: 6px 8px;
    width: calc(100% - 16px);
    border: none;
    background: rgba(255, 255, 255, 0.7);
    color: #333;
    font-size: 14px;
    font-weight: 500;
    text-align: left;
    cursor: pointer;
    transition: all 0.25s cubic-bezier(0.25, 1, 0.5, 1);
    position: relative;
    overflow: hidden;
    border-radius: 8px;
    box-shadow:
      0 2px 6px rgba(107, 72, 255, 0.05),
      0 1px 3px rgba(168, 85, 247, 0.05),
      inset 0 0 0 1px rgba(255, 255, 255, 0.7);

    &:not(:last-child) {
      margin-bottom: 4px;
    }

    i {
      display: flex;
      align-items: center;
      justify-content: center;
      margin-right: 12px;
      font-size: 15px;
      color: #6b48ff;
      transition: all 0.25s ease;
      background: rgba(107, 72, 255, 0.1);
      width: 28px;
      height: 28px;
      border-radius: 6px;
    }

    &:before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: linear-gradient(135deg, rgba(107, 72, 255, 0.1), rgba(168, 85, 247, 0.1));
      opacity: 0;
      transition: opacity 0.25s ease;
      border-radius: 8px;
      z-index: 0;
    }

    &:hover {
      transform: translateY(-2px);
      box-shadow:
        0 6px 15px rgba(107, 72, 255, 0.1),
        0 3px 8px rgba(168, 85, 247, 0.1),
        inset 0 0 0 1px rgba(255, 255, 255, 0.9);
      color: #6b48ff;

      &:before {
        opacity: 1;
      }

      i {
        transform: scale(1.1);
        background: rgba(107, 72, 255, 0.2);
        color: #5338c3;
      }
    }

    &:active {
      transform: translateY(0);
      box-shadow:
        0 2px 6px rgba(107, 72, 255, 0.1),
        0 1px 3px rgba(168, 85, 247, 0.1),
        inset 0 0 0 1px rgba(255, 255, 255, 0.7);
    }

    // Special styling for remove option
    &.remove-option {
      i {
        color: #ff4d4f;
        background: rgba(255, 77, 79, 0.1);
      }

      &:hover {
        color: #ff4d4f;

        &:before {
          background: linear-gradient(135deg, rgba(255, 77, 79, 0.1), rgba(255, 77, 79, 0.1));
        }

        i {
          background: rgba(255, 77, 79, 0.2);
          color: #d32f2f;
        }
      }
    }
  }

  .edit-avatar-btn, .remove-avatar-btn {
    background-color: white;
    color: #6b48ff;
    border: none;
    border-radius: 50%;
    width: 36px;
    height: 36px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    margin: 5px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
    transition: all 0.2s ease;

    &:hover {
      transform: scale(1.1);
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
    }

    i {
      font-size: 16px;
    }
  }

  .remove-avatar-btn {
    color: #ff4d4f;
    position: absolute;
    top: 0;
    right: 0;
    margin: 0;
    width: 28px;
    height: 28px;

    i {
      font-size: 14px;
    }
  }
}

.profile-image {
  width: 120px;
  height: 120px;
  border-radius: 50%;
  object-fit: cover;
  border: 4px solid white;
  box-shadow:
    0 4px 10px rgba(0, 0, 0, 0.1),
    0 0 0 1px rgba(107, 72, 255, 0.1);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  background-color: #f0f2f5; /* Facebook-like background color */
  will-change: transform;
  transform: translateZ(0);
  backface-visibility: hidden;
  -webkit-backface-visibility: hidden;

  &:hover {
    transform: scale(1.05) translateZ(0);
    box-shadow:
      0 6px 15px rgba(0, 0, 0, 0.15),
      0 0 0 1px rgba(107, 72, 255, 0.2);
  }
}

.edit-all-btn {
  position: absolute;
  bottom: 0;
  right: 0;
  background-color: #6b48ff;
  color: white;
  border: none;
  border-radius: 20px;
  padding: 6px 12px;
  font-size: 12px;
  cursor: pointer;
  transition: all 0.2s ease;

  &:hover {
    background-color: #5338c3;
    transform: scale(1.05);
  }
}

.edit-all-actions {
  position: absolute;
  bottom: 0;
  right: 0;
  display: flex;
  gap: 8px;

  .save-all-btn, .cancel-all-btn {
    border: none;
    border-radius: 20px;
    padding: 6px 12px;
    font-size: 12px;
    cursor: pointer;
    transition: all 0.2s ease;
  }

  .save-all-btn {
    background-color: #28a745;
    color: white;

    &:hover {
      background-color: #218838;
      transform: scale(1.05);
    }
  }

  .cancel-all-btn {
    background-color: #dc3545;
    color: white;

    &:hover {
      background-color: #c82333;
      transform: scale(1.05);
    }
  }
}

.profile-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;

  h2 {
    margin: 0 0 8px 0;
    font-size: 24px;
    color: #333;
  }

  .last-shopping {
    margin: 0;
    color: #666;
    font-size: 14px;
  }
}

.profile-content {
  padding: 24px;
}

.profile-field {
  margin-bottom: 20px;

  &:last-child {
    margin-bottom: 0;
  }
}

.profile-field-row {
  display: flex;
  gap: 20px;
  margin-bottom: 20px;

  @media (max-width: 576px) {
    flex-direction: column;
    gap: 20px;
  }
}

.half-width {
  flex: 1;
  margin-bottom: 0;
}

.field-label {
  font-size: 14px;
  color: #666;
  margin-bottom: 8px;
  display: flex;
  justify-content: space-between;
  align-items: center;

  .field-instruction {
    font-size: 12px;
    color: #ff7875;
    font-style: italic;
    margin-left: 10px;
  }
}

.field-value-container {
  display: flex;
  align-items: center;
  justify-content: space-between;
  background-color: #f8f9fa;
  border-radius: 8px;
  padding: 12px 16px;
  transition: background-color 0.2s ease;

  &:hover {
    background-color: #f1f3f5;
  }
}

// Specific styling for address field container
.address-field-container {
  min-height: 50px;

  .field-value {
    min-width: 200px;
    word-wrap: break-word;
  }

  .edit-input {
    min-width: 250px;
  }

  .placeholder-text {
    color: #6c757d;
    font-style: italic;
  }
}

// Specific styling for occupation field container
.occupation-field-container {
  min-height: 50px;

  .field-value {
    min-width: 200px;
    word-wrap: break-word;
  }

  .occupation-edit {
    width: 100%;

    .occupation-inputs {
      display: flex;
      flex-direction: column;
      gap: 12px;
    }
  }

  .placeholder-text {
    color: #6c757d;
    font-style: italic;
  }
}

.field-value {
  font-size: 16px;
  color: #333;
  flex: 1;

  .placeholder-text {
    color: #999;
    font-style: italic;
  }
}

.edit-btn {
  background: none;
  border: none;
  color: #6b48ff;
  cursor: pointer;
  padding: 6px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;

  &:hover {
    background-color: rgba(107, 72, 255, 0.1);
    transform: scale(1.1);
  }

  i {
    font-size: 14px;
  }
}

.field-edit {
  display: flex;
  width: 100%;
  gap: 10px;
  align-items: center;
}

.edit-input, .edit-input-all {
  flex: 1;
  padding: 10px 12px;
  border: 1px solid #ddd;
  border-radius: 6px;
  font-size: 14px;
  transition: border-color 0.2s ease;

  &:focus {
    outline: none;
    border-color: #6b48ff;
    box-shadow: 0 0 0 2px rgba(107, 72, 255, 0.1);
  }
}

// Phone input styling
.phone-input-container {
  position: relative;
  flex: 1;
  display: flex;
  align-items: center;
  flex-direction: column;

  .phone-prefix {
    position: absolute;
    left: 12px;
    top: 10px;
    color: #333;
    font-weight: 500;
    z-index: 2;
    pointer-events: none;
    user-select: none;
  }

  .phone-input {
    padding-left: 60px; // Make room for the country code
    letter-spacing: 0.5px;
    width: 100%;

    &::placeholder {
      color: #aaa;
      opacity: 0.7;
      letter-spacing: 1px;
    }

    &::selection {
      background-color: rgba(107, 72, 255, 0.2);
    }

    &.error-input {
      border-color: #ff4d4f;

      &:focus {
        box-shadow: 0 0 0 2px rgba(255, 77, 79, 0.2);
      }
    }
  }

  .phone-error-message {
    color: #ff4d4f;
    font-size: 12px;
    margin-top: 4px;
    align-self: flex-start;
    animation: fadeIn 0.3s ease;
  }

  @keyframes fadeIn {
    from { opacity: 0; transform: translateY(-5px); }
    to { opacity: 1; transform: translateY(0); }
  }
}

.edit-input-all {
  width: 100%;
  margin-top: 8px;
}

// Occupation specific styles
.occupation-edit, .occupation-inputs-all {
  width: 100%;

  label {
    display: block;
    font-size: 12px;
    color: #666;
    margin-bottom: 4px;
    font-weight: 500;
  }

  .occupation-type, .occupation-type-all {
    margin-bottom: 15px;

    select {
      width: 100%;
      padding: 8px 12px;
      border: 1px solid #ddd;
      border-radius: 4px;
      font-size: 14px;
      background-color: white;
      cursor: pointer;

      &:focus {
        border-color: #6b48ff;
        outline: none;
      }
    }
  }

  .occupation-place, .occupation-place-all {
    margin-top: 5px;

    input {
      width: 100%;
    }
  }
}

.edit-actions {
  display: flex;
  gap: 8px;
}

.save-btn, .cancel-btn, .save-all-btn, .cancel-all-btn {
  padding: 8px 12px;
  border: none;
  border-radius: 6px;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.save-btn, .save-all-btn {
  background-color: #6b48ff;
  color: white;

  &:hover {
    background-color: #5338c3;
  }
}

.cancel-btn, .cancel-all-btn {
  background-color: #f1f3f5;
  color: #666;

  &:hover {
    background-color: #e9ecef;
  }
}

.edit-all-actions {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  margin-top: 24px;
}

.save-all-btn, .cancel-all-btn {
  padding: 10px 20px;
}

// Image Repositioning Modal
.image-reposition-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.7);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  animation: fadeIn 0.3s ease;
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

/* Optimize animations with will-change */
.image-reposition-modal {
  will-change: opacity;
}

.modal-content {
  background-color: white;
  border-radius: 16px;
  width: 90%;
  max-width: 500px;
  max-height: 90vh;
  overflow: hidden;
  box-shadow:
    0 15px 40px rgba(107, 72, 255, 0.2),
    0 8px 20px rgba(168, 85, 247, 0.15),
    0 0 0 1px rgba(171, 85, 247, 0.1);
  display: flex;
  flex-direction: column;
  animation: modalSlideIn 0.3s ease;

  /* Extreme performance optimizations */
  will-change: transform;
  transform: translateZ(0);
  -webkit-transform: translateZ(0);
  backface-visibility: hidden;
  -webkit-backface-visibility: hidden;
  perspective: 1000;
  -webkit-perspective: 1000;
}

@keyframes modalSlideIn {
  from { transform: translate3d(0, 20px, 0); opacity: 0; }
  to { transform: translate3d(0, 0, 0); opacity: 1; }
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  border-bottom: 1px solid #eee;

  h3 {
    margin: 0;
    color: #333;
    font-size: 18px;
  }

  .close-modal-btn {
    background: none;
    border: none;
    color: #666;
    font-size: 18px;
    cursor: pointer;
    padding: 5px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    transition: all 0.2s ease;

    &:hover {
      background-color: #f1f3f5;
      color: #333;
    }
  }
}

.modal-body {
  padding: 20px;
  overflow-y: auto;
  flex: 1;

  .image-container {
    position: relative;
    width: 100%;
    height: 300px;
    display: flex;
    align-items: center;
    justify-content: center;
    overflow: hidden;
    background-color: #f8f9fa;
    border-radius: 8px;
    margin-bottom: 16px;
    user-select: none;
    touch-action: none;
    will-change: contents;
    transform: translateZ(0);
    -webkit-transform: translateZ(0);
    backface-visibility: hidden;
    -webkit-backface-visibility: hidden;
    perspective: 1000;
    -webkit-perspective: 1000;
  }

  .image-preview-container {
    position: relative;
    width: 120px;
    height: 120px;
    border-radius: 50%;
    overflow: hidden;
    box-shadow: 0 0 0 4px white, 0 4px 10px rgba(0, 0, 0, 0.1);
    will-change: transform;
    transform: translateZ(0);
    -webkit-transform: translateZ(0);
  }

  .image-preview {
    width: 100%;
    height: auto;
    min-height: 100%;
    object-fit: cover;
    cursor: grab;
    /* Remove transition for direct manipulation */
    will-change: transform;
    transform: translateZ(0);
    backface-visibility: hidden;
    -webkit-backface-visibility: hidden;
    perspective: 1000;
    -webkit-perspective: 1000;
    /* Force GPU acceleration */
    -webkit-transform-style: preserve-3d;
    transform-style: preserve-3d;

    &.grabbing {
      cursor: grabbing;
    }
  }

  .image-mask {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    border-radius: 50%;
    box-shadow: inset 0 0 0 1px rgba(0, 0, 0, 0.1);
    pointer-events: none;
  }

  .reposition-instructions {
    text-align: center;
    color: #666;
    font-size: 14px;
    margin-top: 10px;
  }
}

.modal-footer {
  padding: 16px 20px;
  border-top: 1px solid #eee;
  display: flex;
  justify-content: flex-end;
  gap: 12px;

  .save-image-btn {
    background-color: #6b48ff;
    color: white;
    border: none;
    border-radius: 6px;
    padding: 10px 20px;
    font-size: 14px;
    cursor: pointer;
    transition: all 0.2s ease;

    &:hover {
      background-color: #5338c3;
    }
  }

  .cancel-btn {
    background-color: #f1f3f5;
    color: #666;
    border: none;
    border-radius: 6px;
    padding: 10px 20px;
    font-size: 14px;
    cursor: pointer;
    transition: all 0.2s ease;

    &:hover {
      background-color: #e9ecef;
    }
  }
}

// Dark mode support
:host-context([data-theme="dark"]) {
  .profile-container {
    background-color: #1a1a1a;
  }

  .profile-card {
    background-color: #2a2a2a;
    border: 1px solid rgba(171, 85, 247, 0.15);
    box-shadow:
      0 10px 30px rgba(0, 0, 0, 0.3),
      0 6px 15px rgba(107, 72, 255, 0.1),
      0 0 0 1px rgba(171, 85, 247, 0.05),
      inset 0 0 0 1px rgba(255, 255, 255, 0.05);

    &::before {
      background: linear-gradient(135deg, rgba(107, 72, 255, 0.2), rgba(168, 85, 247, 0.2), rgba(183, 148, 244, 0.2));
      opacity: 0.3;
    }

    &:hover {
      box-shadow:
        0 15px 40px rgba(0, 0, 0, 0.4),
        0 8px 20px rgba(107, 72, 255, 0.15),
        0 0 0 1px rgba(171, 85, 247, 0.1),
        inset 0 0 0 1px rgba(255, 255, 255, 0.1);
    }
  }

  .profile-header {
    background: linear-gradient(135deg, #2a2a2a, #333);
    border-bottom: 1px solid #444;
  }

  .profile-image {
    border: 4px solid #333;
  }

  .avatar-actions {
    background-color: rgba(0, 0, 0, 0.5);

    .edit-avatar-btn, .remove-avatar-btn {
      background-color: #333;
      color: #a855f7;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.4);

      &:hover {
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.6);
      }
    }

    .remove-avatar-btn {
      color: #ff7875;
    }
  }

  .profile-info {
    h2 {
      color: #fff;
    }

    .last-shopping {
      color: #bbb;
    }
  }

  .field-label {
    color: #bbb;

    .field-instruction {
      color: #ff7875;
    }
  }

  .field-value-container {
    background-color: #333;

    &:hover {
      background-color: #3a3a3a;
    }
  }

  // Dark mode for address field container
  .address-field-container {
    background-color: #333;

    &:hover {
      background-color: #3a3a3a;
    }

    .placeholder-text {
      color: #888;
    }
  }

  // Dark mode for occupation field container
  .occupation-field-container {
    background-color: #333;

    &:hover {
      background-color: #3a3a3a;
    }

    .placeholder-text {
      color: #888;
    }
  }

  .field-value {
    color: #fff;

    .placeholder-text {
      color: #888;
    }
  }

  .edit-input, .edit-input-all {
    background-color: #333;
    border-color: #555;
    color: #fff;

    &:focus {
      border-color: #6b48ff;
    }

    &.phone-input {
      &::selection {
        background-color: rgba(107, 72, 255, 0.4);
        color: #fff;
      }
    }
  }

  // Dark mode for phone input
  .phone-input-container {
    .phone-prefix {
      color: #ddd;
    }

    .phone-input {
      &::placeholder {
        color: #666;
      }

      &.error-input {
        border-color: #ff7875;

        &:focus {
          box-shadow: 0 0 0 2px rgba(255, 120, 117, 0.2);
        }
      }
    }

    .phone-error-message {
      color: #ff7875;
    }
  }

  // Dark mode for occupation fields
  .occupation-edit, .occupation-inputs-all {
    label {
      color: #bbb;
    }

    .occupation-type, .occupation-type-all {
      select {
        background-color: #333;
        border-color: #555;
        color: #fff;

        &:focus {
          border-color: #6b48ff;
        }

        option {
          background-color: #333;
          color: #fff;
        }
      }
    }

    .occupation-place, .occupation-place-all {
      input {
        background-color: #333;
        border-color: #555;
        color: #fff;
      }
    }
  }

  .cancel-btn, .cancel-all-btn {
    background-color: #444;
    color: #ddd;

    &:hover {
      background-color: #555;
    }
  }

  .edit-all-actions {
    .save-all-btn {
      background-color: #28a745;

      &:hover {
        background-color: #218838;
      }
    }

    .cancel-all-btn {
      background-color: #444;
      color: #ddd;

      &:hover {
        background-color: #555;
      }
    }
  }

  // Dark mode for image repositioning modal
  .modal-content {
    background-color: #2a2a2a;
    box-shadow:
      0 15px 40px rgba(0, 0, 0, 0.4),
      0 8px 20px rgba(107, 72, 255, 0.15),
      0 0 0 1px rgba(171, 85, 247, 0.1);
  }

  .modal-header {
    border-bottom: 1px solid #444;

    h3 {
      color: #fff;
    }

    .close-modal-btn {
      color: #bbb;

      &:hover {
        background-color: #444;
        color: #fff;
      }
    }
  }

  .modal-body {
    .image-container {
      background-color: #333;
    }

    .image-preview-container {
      box-shadow: 0 0 0 4px #333, 0 4px 10px rgba(0, 0, 0, 0.3);
    }

    .image-mask {
      box-shadow: inset 0 0 0 1px rgba(255, 255, 255, 0.1);
    }

    .reposition-instructions {
      color: #bbb;
    }
  }

  .modal-footer {
    border-top: 1px solid #444;
  }
}