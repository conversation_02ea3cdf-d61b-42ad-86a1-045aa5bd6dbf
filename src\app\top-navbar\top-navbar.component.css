@import url('https://fonts.googleapis.com/css2?family=Inter:wght@400;500&display=swap');
@import url('https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css');

.top-navbar {
  position: fixed;
  top: 0;
  right: 0;
  left: 250px;
  height: 64px;
  background: var(--card-bg, white);
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 16px;
  z-index: 999;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
  /* Hardware acceleration */
  transform: translateZ(0);
  backface-visibility: hidden;
  will-change: transform;
}

.navbar-left {
  display: flex;
  align-items: center;
  gap: 16px;
}

.navbar-title {
  font-size: 1.2rem;
  font-weight: 600;
  color: var(--text-color, #333);
  margin: 0;
}



.navbar-right {
  display: flex;
  align-items: center;
  gap: 16px;
  max-width: 320px;
  flex-wrap: nowrap;
  overflow-x: auto;
  scrollbar-width: none;
  -ms-overflow-style: none;
  /* Performance optimizations */
  transform: translateZ(0);
  will-change: transform, opacity;
  transition: transform 0.2s ease-out, opacity 0.2s ease-out;
}

.navbar-right::-webkit-scrollbar {
  display: none;
}

.date {
  font-family: 'Inter', sans-serif;
  font-size: 0.75rem;
  font-weight: 500;
  color: #ffffff;
  background: linear-gradient(135deg, #6b21a8, #9333ea);
  padding: 4px 8px;
  border-radius: 6px;
  box-shadow: 0 2px 4px rgba(107, 33, 168, 0.3);
  height: 24px;
  max-width: 90px;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  clip-path: inset(0);
  transition: transform 0.2s ease, box-shadow 0.2s ease;
  white-space: nowrap;
  overflow: hidden;
}

.date:hover {
  transform: scale(1.02);
  box-shadow: 0 3px 6px rgba(107, 33, 168, 0.4);
}

.date i {
  margin-right: 4px;
  color: #ffffff;
  font-size: 0.7rem;
}

/* Using transform instead of layout properties when possible */
@media (max-width: 768px) {
  .top-navbar {
    left: 0;
    padding: 0 12px;
    height: 56px;
    transition: height 0.2s ease-out;
  }

  .dropdown-trigger {
    display: block;
    position: absolute;
    top: 38px;
    left: 50%;
    transform: translateX(-50%);
    width: 20px;
    height: 20px;
    cursor: pointer;
    z-index: 10;
  }

  .dropdown-arrow {
    width: 20px;
    height: 20px;
    color: #666;
  }

  .dropdown-arrow.open {
    transform: rotate(180deg);
  }

  .mobile-menu-button {
    display: flex;
    flex-direction: column;
    align-items: center;
    position: relative;
    padding-bottom: 24px;
  }

  .navbar-right {
    position: absolute;
    top: 100%;
    right: 0;
    background: var(--card-bg, white);
    padding: 8px;
    border-radius: 0 0 8px 8px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    opacity: 0;
    pointer-events: none;
    min-width: 160px;
    flex-direction: column;
    gap: 12px;
    overflow-x: visible;
    z-index: 9;
    transform: translateY(-10px);
    transition: opacity 0.2s ease, transform 0.2s ease;
  }

  .navbar-right.open {
    display: flex;
    align-items: flex-end;
    opacity: 1;
    pointer-events: all;
    transform: translateY(0);
  }

  .navbar-right i {
    width: 100%;
    text-align: right;
    padding: 6px 8px;
    font-size: 14px;
    display: flex;
    justify-content: flex-end;
    align-items: center;
    gap: 6px;
  }

  .navbar-right i::after {
    content: attr(data-label);
    font-family: inherit;
    font-size: 12px;
    color: var(--text-color, #666);
  }

  .date {
    font-size: 0.7rem;
    padding: 3px 6px;
    height: 20px;
    max-width: 80px;
  }

  .date i {
    margin-right: 3px;
    font-size: 0.65rem;
  }
}

@media (max-width: 480px) {
  .top-navbar {
    height: 48px;
  }

  .dropdown-trigger {
    top: 32px;
  }

  .mobile-menu-button {
    padding-bottom: 20px;
  }

  .date {
    font-size: 0.65rem;
    padding: 2px 5px;
    height: 18px;
    max-width: 70px;
  }

  .date i {
    margin-right: 2px;
    font-size: 0.6rem;
  }
}

.navbar-right i {
  color: var(--text-color, #666);
  font-size: 1.1rem;
  cursor: pointer;
  transition: all 0.3s ease;
  flex-shrink: 0;
}

.navbar-right i:hover {
  color: #6b48ff;
  transform: scale(1.1);
}

.avatar-container {
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  flex-shrink: 0;
}

.header-avatar {
  width: 36px;
  height: 36px;
  border-radius: 50%;
  cursor: pointer;
  transition: transform 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: var(--secondary-bg, #f0f0f0);
}

.header-avatar:hover {
  transform: scale(1.1);
}

.avatar-icon {
  width: 24px;
  height: 24px;
  color: var(--text-color, #666);
}

.avatar-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 50%;
}

.mobile-menu-button {
  display: none;
}

@media (max-width: 768px) {
  .mobile-menu-button {
    display: flex;
  }
}

.theme-toggle {
  background: none;
  border: none;
  color: var(--text-color, #333);
  cursor: pointer;
  padding: 6px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: background-color 0.3s ease;
  flex-shrink: 0;
}

.theme-toggle:hover {
  background-color: var(--hover-bg, #f0f0f0);
}

.theme-toggle i {
  font-size: 1.1rem;
}

.notifications, .user-profile {
  color: var(--text-color, #333);
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(-10px);
    box-shadow: 0 0px 0px rgba(255, 192, 203, 0);
  }
  to {
    opacity: 1;
    transform: translateY(0);
    box-shadow: 0 4px 20px rgba(255, 192, 203, 0.4);
  }
}

:host {
  display: block;
  height: 64px;
}

.main-content {
  margin-top: 64px;
  padding: 16px;
}

@media (max-width: 768px) {
  :host {
    height: 56px;
  }

  .main-content {
    margin-top: 56px;
    padding: 12px;
  }
}

@media (max-width: 480px) {
  :host {
    height: 48px;
  }

  .main-content {
    margin-top: 48px;
    padding: 8px;
  }
}