import { NgModule } from '@angular/core';
import { BrowserModule } from '@angular/platform-browser';
import { CommonModule } from '@angular/common'; // Added
import { AppRoutingModule } from './app-routing.module';
import { AppComponent } from './app.component';
import { WelcomeToReceetoComponent } from './welcome-to-receeto/welcome-to-receeto.component';
import { LoginComponent } from './login/login.component';
import { SharedModule } from './shared/shared.module';

@NgModule({
  declarations: [
    AppComponent,
    WelcomeToReceetoComponent,
    LoginComponent,
    // Removed ShopperAnalyticsComponent as it's now in its own module
  ],
  imports: [
    BrowserModule,
    AppRoutingModule,
    SharedModule,
    CommonModule // Moved from declarations to imports
  ],
  providers: [],
  bootstrap: [AppComponent]
})
export class AppModule { }
