<app-top-navbar></app-top-navbar>
<app-sidebar></app-sidebar>

<div class="dashboard-container">
  <div class="main-content">
    <!-- Lifetime Expenses Card -->
    <div class="card lifetime-expenses">
      <div class="card-header">
        <h2>Life Time Expenses</h2>
      </div>
      <div class="amount">233.200.00 TND</div>
      <div class="meta-info">
        <div class="articles">5667 Articles</div>
        <div class="time">5Y 25D 12H</div>
      </div>
    </div>

    <!-- Current Month Expenses Card -->
    <div class="card current-month">
      <div class="card-header">
        <h2>Expenses Current Month</h2>
      </div>
      <div class="amount">1200.00 TND</div>
      <div class="meta-info">
        <div class="articles">244 Articles</div>
        <div class="time">25D 12H</div>
      </div>
    </div>

    <!-- Expenses Category Card -->
    <div class="card expenses-category">
      <div class="card-header">
        <div class="title-wrapper">
          <h2>Expenses</h2>
          <div class="subtitle">Per Category</div>
        </div>
        <div class="dropdown-selector">1 month</div>
      </div>
      <div class="chart-container">
        <canvas id="expensesCategoryChart"></canvas>
        <div class="chart-center">
          <div class="view-details">View details</div>
        </div>
      </div>
      <div class="chart-legend">
        <div class="legend-item">
          <div class="color-indicator food"></div>
          <span>Food</span>
        </div>
        <div class="legend-item">
          <div class="color-indicator clothes"></div>
          <span>Clothes</span>
        </div>
        <div class="legend-item">
          <div class="color-indicator leisure"></div>
          <span>Leisure</span>
        </div>
        <div class="legend-item">
          <div class="color-indicator medicines"></div>
          <span>Medicines</span>
        </div>
      </div>
    </div>

    <!-- Expenses Per Month Card -->
    <div class="card expenses-month">
      <div class="card-header">
        <h2>Expenses Per Month</h2>
        <button class="view-all">View All</button>
      </div>
      <div class="amount">$ 1544.00</div>
      <div class="chart-container">
        <canvas id="expensesMonthChart"></canvas>
      </div>
    </div>

    <!-- Last Transactions Card -->
    <div class="card transactions">
      <div class="card-header">
        <h2>Last Transactions</h2>
        <button class="view-all">View All</button>
      </div>
      <div class="transaction-list">
        <div class="transaction-item" *ngFor="let transaction of transactions">
          <div class="transaction-left">
            <div class="indicator"></div>
            <div class="transaction-date">{{ transaction.date }}, {{ transaction.time }}</div>
          </div>
          <div class="amount">{{ transaction.amount }} TND</div>
        </div>
      </div>
    </div>

    <!-- Saving Plan Card -->
    <div class="card saving-plan">
      <div class="card-header">
        <h2>Saving Plan</h2>
        <div class="menu-dots">⋮</div>
      </div>
      <div class="chart-container">
        <canvas id="savingPlanChart"></canvas>
        <div class="chart-center">
          <div class="saving-label">Total Saving</div>
          <div class="saving-sublabel">Label</div>
        </div>
      </div>
      <div class="chart-legend">
        <div class="legend-item">
          <div class="color-indicator product1"></div>
          <span>Product 1</span>
        </div>
        <div class="legend-item">
          <div class="color-indicator product2"></div>
          <span>Product 2</span>
        </div>
        <div class="legend-item">
          <div class="color-indicator product3"></div>
          <span>Product 3</span>
        </div>
        <div class="legend-item">
          <div class="color-indicator product4"></div>
          <span>Product 4</span>
        </div>
      </div>
    </div>
  </div>
</div>