<!-- Show email verification first -->
<app-email-verification
  *ngIf="!isEmailVerified"
  [email]="email"
  (emailVerified)="onEmailVerified()"
  (skipped)="skipEmailVerification()">
</app-email-verification>

<!-- Show personal info form after email is verified -->
<div *ngIf="isEmailVerified" class="container light-mode-only">
  <div class="left-section">
    <a class="back-link" (click)="goBack()">← Back to website</a>
    <h1>Personal Information</h1>
    <p>Please provide your personal details.</p>
    <form (ngSubmit)="onSubmit()" #personalInfoForm="ngForm">
      <input type="text" placeholder="Full Name" name="fullName" [(ngModel)]="model.fullName" required>
      <input type="date" placeholder="Birthday" name="birthday" [(ngModel)]="model.birthday" required>
      <select name="governorate" [(ngModel)]="model.governorate" required>
        <option value="" disabled selected>Select Governorate</option>
        <option value="governorate1">Governorate 1</option>
        <option value="governorate2">Governorate 2</option>
      </select>
      <select name="city" [(ngModel)]="model.city" required>
        <option value="" disabled selected>Select City</option>
        <option value="city1">City 1</option>
        <option value="city2">City 2</option>
      </select>
      <div class="gender">
        <label>Gender:</label>
        <input type="radio" name="gender" value="male" [(ngModel)]="model.gender" required> Male
        <input type="radio" name="gender" value="female" [(ngModel)]="model.gender"> Female
      </div>
      <button type="submit" class="submit-btn" [disabled]="!personalInfoForm.valid">SUBMIT</button>
    </form>
    <footer>© 2023 Receeto ALL RIGHTS RESERVED.</footer>
  </div>
  <div class="right-section">
    <h2>Receeto™</h2>
    <nav>
      <a href="#">WEBSITE</a>
      <a href="#">DOCUMENTATION</a>
      <a href="#">TERMS OF USE</a>
      <a href="#">BLOG</a>
    </nav>
  </div>
</div>