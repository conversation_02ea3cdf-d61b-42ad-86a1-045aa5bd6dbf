import { Component, Input, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';

interface ExpenseItem {
  name: string;
  amount: number;
  percentage: number;
}

@Component({
  selector: 'app-expense-list',
  standalone: true,
  imports: [CommonModule],
  template: `
    <div class="expense-list">
      <h3 class="list-title">Top Expenses {{ suffix }}</h3>
      <div class="expense-items">
        <div class="expense-item" *ngFor="let item of expenses; let i = index">
          <div class="expense-info">
            <span class="name">{{ i + 1 }} {{ item.name }}</span>
            <span class="amount">{{ item.amount }} TND</span>
          </div>
          <div class="progress-bar">
            <div class="progress" [style.width.%]="item.percentage"></div>
          </div>
          <span class="percentage">{{ item.percentage }}% <span class="percentage-arrow">↑</span></span>
        </div>
      </div>
    </div>
  `,
  styles: [`
    .expense-list {
      margin-top: 24px;
    }

    .list-title {
      font-size: 1.25rem;
      font-weight: 600;
      color: #2d3748;
      margin-bottom: 16px;
    }

    .expense-items {
      display: flex;
      flex-direction: column;
      gap: 16px;
    }

    .expense-item {
      display: flex;
      flex-direction: column;
      gap: 8px;
    }

    .expense-info {
      display: flex;
      justify-content: space-between;
      align-items: center;
    }

    .name {
      font-weight: 500;
      color: #4a5568;
    }

    .amount {
      font-weight: 600;
      color: #2d3748;
    }

    .progress-bar {
      height: 4px;
      background: #edf2f7;
      border-radius: 2px;
      overflow: hidden;
    }

    .progress {
      height: 100%;
      background: #6b48ff;
      border-radius: 2px;
      transition: width 0.3s ease;
    }

    .percentage {
      font-size: 0.875rem;
      color: #718096;
      display: flex;
      align-items: center;
      gap: 4px;
    }

    .percentage-arrow {
      color: #48bb78;
    }
  `]
})
export class ExpenseListComponent implements OnInit {
  @Input() expenses: ExpenseItem[] = [];
  @Input() suffix: string = '';

  ngOnInit() {
    this.sortExpenses();
  }

  sortExpenses() {
    this.expenses.sort((a, b) => b.amount - a.amount);
  }
}