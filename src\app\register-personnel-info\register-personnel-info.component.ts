import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { Router, ActivatedRoute } from '@angular/router';
import { EmailVerificationComponent } from '../email-verification/email-verification.component';
import { Location } from '@angular/common';

@Component({
  selector: 'app-register-personnel-info',
  standalone: true,
  imports: [CommonModule, FormsModule, EmailVerificationComponent],
  templateUrl: './register-personnel-info.component.html',
  styleUrls: ['./register-personnel-info.component.css']
})
export class RegisterPersonnelInfoComponent implements OnInit {
  isEmailVerified: boolean = false;
  email: string = 'n****@e*****.com';
  role: string = '';

  // Update the model to match the form fields in the HTML
  model = {
    fullName: '',
    birthday: '',
    governorate: '',
    city: '',
    gender: ''
  };

  constructor(
    private router: Router,
    private location: Location,
    private route: ActivatedRoute
  ) {}

  ngOnInit() {
    this.route.queryParams.subscribe(params => {
      this.role = params['role'] || '';
    });
  }

  goBack() {
    this.location.back();
  }

  onEmailVerified() {
    this.isEmailVerified = true;
  }

  skipEmailVerification() {
    this.isEmailVerified = true;
  }

  onSubmit() {
    if (this.model.fullName && this.model.birthday && this.model.governorate && this.model.city && this.model.gender) {
      console.log('Personal info submitted:', this.model);
      if (this.role === 'shopper') {
        this.router.navigate(['/shopper-dashboard']);
      } else if (this.role === 'seller') {
        this.router.navigate(['/seller-dashboard']);
      } else {
        this.router.navigate(['/dashboard']);
      }
    } else {
      console.log('Please fill in all required fields.');
    }
  }
}