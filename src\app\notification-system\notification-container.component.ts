import { Component, OnInit, OnD<PERSON>roy } from '@angular/core';
import { CommonModule } from '@angular/common';
import { NotificationPanelComponent } from './notification-panel.component';
import { NotificationService } from './notification.service';
import { Subject } from 'rxjs';
import { takeUntil } from 'rxjs/operators';

@Component({
  selector: 'app-notification-container',
  standalone: true,
  imports: [CommonModule, NotificationPanelComponent],
  template: `
    <app-notification-panel *ngIf="showNotifications"></app-notification-panel>
  `,
  styles: []
})
export class NotificationContainerComponent implements OnInit, OnDestroy {
  showNotifications = false;
  private destroy$ = new Subject<void>();

  constructor(private notificationService: NotificationService) {}

  ngOnInit(): void {
    // Subscribe to notification visibility
    this.notificationService.showNotifications$
      .pipe(takeUntil(this.destroy$))
      .subscribe(show => {
        this.showNotifications = show;
      });
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }
}
