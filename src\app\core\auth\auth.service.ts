import { Injectable, signal, computed, inject } from '@angular/core';
import { Observable, of } from 'rxjs';
import { AuthUser } from '../../interfaces/auth-user';
import { Login } from '../../interfaces/login';
import { CreationAccount } from '../../interfaces/creation-account';
import { AccountNotificationService } from '../account-notifications/account-notification.service';
import { NotificationService } from '../../notification-system/notification.service';

@Injectable({
  providedIn: 'root'
})
export class AuthService {
  // Auth state signals
  private _isAuthenticated = signal<boolean>(false);
  private _currentUser = signal<AuthUser | null>(null);
  private _isLoading = signal<boolean>(false);

  // Public readonly signals
  readonly isAuthenticated = this._isAuthenticated.asReadonly();
  readonly currentUser = this._currentUser.asReadonly();
  readonly isLoading = this._isLoading.asReadonly();

  // Computed signals
  readonly userFullName = computed(() => this._currentUser()?.full_name || '');
  readonly userEmail = computed(() => this._currentUser()?.email || '');
  readonly userAvatar = computed(() => this._currentUser()?.avatar || 'assets/images/default-avatar.svg');

  constructor(
    private accountNotificationService: AccountNotificationService,
    private notificationService: NotificationService
  ) {
    this.initializeAuth();
  }

  private initializeAuth(): void {
    // DO NOT clear localStorage - we need persistent user data
    // Try to load user from localStorage
    this.loadUserFromStorage();
  }

  private loadUserFromStorage(): void {
    try {
      const storedUser = localStorage.getItem('authUser');
      const isAuth = localStorage.getItem('isAuthenticated') === 'true';

      if (storedUser && isAuth) {
        const user: AuthUser = JSON.parse(storedUser);
        this._currentUser.set(user);
        this._isAuthenticated.set(true);

        // Set current user for notification services
        this.accountNotificationService.setCurrentUser(user.email);
        this.notificationService.setCurrentUser(user.email);

        console.log('User loaded from localStorage:', user);
      }
    } catch (error) {
      console.error('Error loading user from localStorage:', error);
      this.logout();
    }
  }

  private saveUserToStorage(user: AuthUser): void {
    try {
      localStorage.setItem('authUser', JSON.stringify(user));
      localStorage.setItem('isAuthenticated', 'true');
    } catch (error) {
      console.error('Error saving user to localStorage:', error);
    }
  }

  private saveRegisteredUserToStorage(user: AuthUser, password: string): void {
    try {
      // Get existing registered users or initialize empty object
      const existingUsers = this.getAllRegisteredUsers();

      // Save user data with password for validation, keyed by email
      const userData = { ...user, password: password };
      existingUsers[user.email] = userData;

      localStorage.setItem('registeredUsers', JSON.stringify(existingUsers));
      console.log('User registration data saved to localStorage for email:', user.email);
    } catch (error) {
      console.error('Error saving registered user to localStorage:', error);
    }
  }

  private getRegisteredUserFromStorage(email: string): any | null {
    try {
      const allUsers = this.getAllRegisteredUsers();
      return allUsers[email] || null;
    } catch (error) {
      console.error('Error loading registered user from localStorage:', error);
      return null;
    }
  }

  private getAllRegisteredUsers(): { [email: string]: any } {
    try {
      const storedUsers = localStorage.getItem('registeredUsers');
      return storedUsers ? JSON.parse(storedUsers) : {};
    } catch (error) {
      console.error('Error loading all registered users from localStorage:', error);
      return {};
    }
  }

  register(accountData: CreationAccount, role?: string): Observable<AuthUser> {
    this._isLoading.set(true);

    // Simulate API call
    return new Observable<AuthUser>(observer => {
      setTimeout(() => {
        try {
          // Check if user already exists
          const existingUser = this.getRegisteredUserFromStorage(accountData.email);
          if (existingUser) {
            this._isLoading.set(false);
            observer.error(new Error('Email already registered. Please use a different email or login with existing account.'));
            return;
          }

          // Validate password strength
          if (accountData.password.length < 8) {
            this._isLoading.set(false);
            observer.error(new Error('Password is too weak. Please use at least 8 characters.'));
            return;
          }

          const newUser: AuthUser = {
            full_name: `${accountData.firstName} ${accountData.lastName}`,
            email: accountData.email,
            avatar: 'assets/images/default-avatar.svg',
            is_initialized: true,
            role: role || 'user'
          };

          // Save user data with password
          this.saveRegisteredUserToStorage(newUser, accountData.password);

          // Temporarily authenticate user for sup-info flow
          this._currentUser.set(newUser);
          this._isAuthenticated.set(true);
          this.saveUserToStorage(newUser);
          this._isLoading.set(false);

          // Set current user for notification services and create welcome notification
          this.accountNotificationService.setCurrentUser(newUser.email);
          this.notificationService.setCurrentUser(newUser.email);
          this.accountNotificationService.createWelcomeNotification();

          console.log('User registered and temporarily authenticated for profile completion:', newUser);
          observer.next(newUser);
          observer.complete();
        } catch (error) {
          this._isLoading.set(false);
          observer.error(new Error('Registration failed. Please try again.'));
        }
      }, 1000);
    });
  }

  login(loginData: Login): Observable<AuthUser> {
    this._isLoading.set(true);

    // Check if user exists in registered users and validate password
    return new Observable<AuthUser>(observer => {
      setTimeout(() => {
        try {
          // Get user data by email
          const userData = this.getRegisteredUserFromStorage(loginData.email);

          if (userData) {
            // Validate password
            if (userData.password === loginData.password) {
              // Remove password from user object before setting
              const { password, ...userWithoutPassword } = userData;
              const authenticatedUser: AuthUser = {
                ...userWithoutPassword,
                // Ensure supplementary info is included
                birthday: userData.birthday,
                gender: userData.gender,
                phoneNumber: userData.phoneNumber,
                // Ensure avatar data is properly loaded for user isolation
                avatar: userData.avatar || 'assets/images/default-avatar.svg',
                hasCustomAvatar: userData.hasCustomAvatar || false,
                avatarPositionY: userData.avatarPositionY || 0
              } as any;

              this._currentUser.set(authenticatedUser);
              this._isAuthenticated.set(true);
              this.saveUserToStorage(authenticatedUser);
              this._isLoading.set(false);

              // Set current user for notification services
              this.accountNotificationService.setCurrentUser(authenticatedUser.email);
              this.notificationService.setCurrentUser(authenticatedUser.email);

              console.log('User logged in successfully with supplementary info:', authenticatedUser);
              observer.next(authenticatedUser);
              observer.complete();
            } else {
              this._isLoading.set(false);
              observer.error(new Error('Invalid email or password. Please check your credentials.'));
            }
          } else {
            this._isLoading.set(false);
            observer.error(new Error('No account found. Please create an account first.'));
          }
        } catch (error) {
          this._isLoading.set(false);
          observer.error(new Error('Login failed. Please try again.'));
        }
      }, 1000);
    });
  }

  logout(): void {
    this._currentUser.set(null);
    this._isAuthenticated.set(false);
    localStorage.removeItem('authUser');
    localStorage.removeItem('isAuthenticated');

    // Clear current user for notification services
    this.accountNotificationService.setCurrentUser(null);
    this.notificationService.clearCurrentUser();

    // Note: We keep 'registeredUser' so they can login again
    console.log('User logged out');
  }

  updateUser(userData: Partial<AuthUser>): Observable<AuthUser> {
    const currentUser = this._currentUser();
    if (currentUser) {
      // Handle sup-info updates
      if (userData.supInfo) {
        const updatedUser = {
          ...currentUser,
          supInfo: {
            ...currentUser.supInfo,
            ...userData.supInfo
          }
        };
        this._currentUser.set(updatedUser);
        this.saveUserToStorage(updatedUser);

        // Also update the registered user data with supplementary info
        this.updateRegisteredUserData(updatedUser);

        console.log('User updated with sup-info:', updatedUser);
        return of(updatedUser);
      } else {
        const updatedUser = { ...currentUser, ...userData };
        this._currentUser.set(updatedUser);
        this.saveUserToStorage(updatedUser);

        // Also update the registered user data with supplementary info
        this.updateRegisteredUserData(updatedUser);

        console.log('User updated:', updatedUser);
        return of(updatedUser);
      }
    }
    return of(null as any);
  }

  private updateRegisteredUserData(user: AuthUser): void {
    try {
      const allUsers = this.getAllRegisteredUsers();
      const userData = allUsers[user.email];

      if (userData) {
        // Update with new information while preserving password
        const updatedData = {
          ...userData,
          full_name: user.full_name,
          email: user.email,
          avatar: user.avatar,
          is_initialized: user.is_initialized,
          role: user.role,
          // Add supplementary information
          birthday: (user as any).birthday,
          gender: (user as any).gender,
          phoneNumber: (user as any).phoneNumber,
          // Add avatar-specific data for proper user isolation
          hasCustomAvatar: (user as any).hasCustomAvatar,
          avatarPositionY: (user as any).avatarPositionY
        };

        allUsers[user.email] = updatedData;
        localStorage.setItem('registeredUsers', JSON.stringify(allUsers));
        console.log('Registered user data updated with supplementary info for:', user.email);
      }
    } catch (error) {
      console.error('Error updating registered user data:', error);
    }
  }
}
