import { Injectable } from '@angular/core';

@Injectable({
  providedIn: 'root'
})
export class AppStorageService {
  // Use memory storage instead of sessionStorage for development
  // This ensures data is reset on page reload
  private memoryStorage: Map<string, any> = new Map<string, any>();

  private readonly SUBSCRIBED_BRANDS = 'subscribed_brands';
  private readonly SUBSCRIBED_EMAIL = 'subscribed_email';
  private readonly BRAND_RATINGS = 'brand_ratings';
  private readonly BUDGETS = 'budgets';
  private readonly BUDGET_CATEGORIES = 'budget_categories';
  private readonly SAVING_PLANS = 'saving_plans';
  private readonly LOYALTY_CARDS = 'loyalty_cards';

  constructor() {
    // Initialize with empty data
    this.clearAllData();
  }

  // Save subscribed brands
  saveSubscribedBrands(brands: string[]): void {
    this.memoryStorage.set(this.SUBSCRIBED_BRANDS, brands);
  }

  // Get subscribed brands
  getSubscribedBrands(): string[] {
    return this.memoryStorage.get(this.SUBSCRIBED_BRANDS) || [];
  }

  // Save subscribed email
  saveSubscribedEmail(email: string): void {
    this.memoryStorage.set(this.SUBSCRIBED_EMAIL, email);
  }

  // Get subscribed email
  getSubscribedEmail(): string | null {
    return this.memoryStorage.get(this.SUBSCRIBED_EMAIL) || null;
  }

  // Save brand ratings
  saveBrandRatings(ratings: [string, number][]): void {
    this.memoryStorage.set(this.BRAND_RATINGS, ratings);
  }

  // Get brand ratings
  getBrandRatings(): [string, number][] {
    return this.memoryStorage.get(this.BRAND_RATINGS) || [];
  }

  // Save budgets
  saveBudgets(budgets: any[]): void {
    this.memoryStorage.set(this.BUDGETS, budgets);
  }

  // Get budgets
  getBudgets(): any[] {
    return this.memoryStorage.get(this.BUDGETS) || [];
  }

  // Save budget categories
  saveBudgetCategories(categories: any[]): void {
    this.memoryStorage.set(this.BUDGET_CATEGORIES, categories);
  }

  // Get budget categories
  getBudgetCategories(): any[] {
    return this.memoryStorage.get(this.BUDGET_CATEGORIES) || [];
  }

  // Save saving plans
  saveSavingPlans(plans: any[]): void {
    this.memoryStorage.set(this.SAVING_PLANS, plans);
  }

  // Get saving plans
  getSavingPlans(): any[] {
    return this.memoryStorage.get(this.SAVING_PLANS) || [];
  }

  // Save loyalty cards
  saveLoyaltyCards(cards: any[]): void {
    this.memoryStorage.set(this.LOYALTY_CARDS, cards);
  }

  // Get loyalty cards
  getLoyaltyCards(): any[] {
    return this.memoryStorage.get(this.LOYALTY_CARDS) || [];
  }

  // Clear all storage data
  clearAllData(): void {
    this.memoryStorage.clear();
    // Initialize with empty data
    this.memoryStorage.set(this.SUBSCRIBED_BRANDS, []);
    this.memoryStorage.set(this.BRAND_RATINGS, []);
    this.memoryStorage.set(this.SUBSCRIBED_EMAIL, null);
    this.memoryStorage.set(this.BUDGETS, []);
    this.memoryStorage.set(this.BUDGET_CATEGORIES, []);
    this.memoryStorage.set(this.SAVING_PLANS, []);
    this.memoryStorage.set(this.LOYALTY_CARDS, []);
  }
}
