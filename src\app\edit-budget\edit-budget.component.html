<app-top-navbar></app-top-navbar>
<app-sidebar></app-sidebar>

<div class="edit-budget-container" [class.dark-mode]="isDarkMode">
  <div class="edit-budget-header">
    <h1>Edit Budget</h1>
  </div>

  <div *ngIf="isLoading" class="loading-indicator">
    <p>Loading budget...</p>
  </div>

  <div *ngIf="notFound" class="not-found-message">
    <p>Budget not found. Please go back to the budget list.</p>
    <button class="back-btn" (click)="cancel()">Back to Budgets</button>
  </div>

  <div *ngIf="!isLoading && !notFound" class="budget-form" [class.dark-mode]="isDarkMode">
    <div class="form-group">
      <label>Budget Name</label>
      <input type="text" [(ngModel)]="budget.name" placeholder="Budget Name" class="form-control">
    </div>

    <div class="form-group">
      <label>Period</label>
      <div class="period-options">
        <div class="period-option" [class.selected]="budget.period === BudgetPeriod.OneMonth" (click)="budget.period = BudgetPeriod.OneMonth">
          1 month
        </div>
        <div class="period-option" [class.selected]="budget.period === BudgetPeriod.ThreeMonths" (click)="budget.period = BudgetPeriod.ThreeMonths">
          3 months
        </div>
        <div class="period-option" [class.selected]="budget.period === BudgetPeriod.SixMonths" (click)="budget.period = BudgetPeriod.SixMonths">
          6 months
        </div>
        <div class="period-option" [class.selected]="budget.period === BudgetPeriod.OneYear" (click)="budget.period = BudgetPeriod.OneYear">
          1 year
        </div>
      </div>
    </div>

    <div class="form-group">
      <label>Budget Amount</label>
      <input type="number" [(ngModel)]="budget.amount" placeholder="Example: 1000 TND" class="form-control">
    </div>

    <div class="form-group">
      <label>Category</label>
      <div class="category-select">
        <select [(ngModel)]="budget.category" class="form-control">
          <option value="" disabled>Select a category</option>
          <option *ngFor="let category of availableCategories" [value]="category">{{ category }}</option>
        </select>
        <div class="select-arrow">▼</div>
      </div>
    </div>

    <div class="form-group">
      <label>Notifications System</label>
      <div class="notification-settings" [class.expanded]="showNotificationSettings">
        <div class="notification-header" (click)="toggleNotificationSettings()">
          <span>Budget overrun</span>
          <div class="toggle-switch">
            <input type="checkbox" id="budgetOverrun" [(ngModel)]="budget.notifications.budgetOverrun">
            <label for="budgetOverrun"></label>
          </div>
        </div>
        <div class="notification-description">
          Warn When The Amount Has Exceeded The Budget
        </div>

        <div class="notification-header">
          <span>Risk of overrun</span>
          <div class="toggle-switch">
            <input type="checkbox" id="riskOverrun" [(ngModel)]="budget.notifications.riskOfOverrun">
            <label for="riskOverrun"></label>
          </div>
        </div>
        <div class="notification-description">
          Warn When Budget May Be Exceeded
        </div>
      </div>
    </div>

    <div class="form-actions">
      <div class="left-actions">
        <button class="delete-btn" (click)="deleteBudget()">Delete</button>
      </div>
      <div class="right-actions">
        <button class="cancel-btn" (click)="cancel()">Cancel</button>
        <button class="save-btn" (click)="updateBudget()">Save Changes</button>
      </div>
    </div>
  </div>
</div>
