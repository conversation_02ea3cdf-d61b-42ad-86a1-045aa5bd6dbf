import { Component, OnInit } from '@angular/core';
import { Router, ActivatedRoute } from '@angular/router';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { RouterModule } from '@angular/router';
import { AuthService } from '../core/auth/auth.service';
import { SupInfo } from '../interfaces/sup-info';
import { SecurityAlertsComponent } from '../security-alerts/security-alerts.component';
import { UserDataService } from '../core/user-data/user-data.service';
import { inject } from '@angular/core';

@Component({
  selector: 'app-sup-info',
  standalone: true,
  imports: [CommonModule, FormsModule, RouterModule, SecurityAlertsComponent],
  templateUrl: './sup-info.component.html',
  styleUrl: './sup-info.component.scss'
})
export class SupInfoComponent implements OnInit {
  supInfoData: SupInfo = {
    birthday: '',
    gender: 'male',
    phoneNumber: '+216'
  };

  errorMessage = '';
  successMessage = '';
  role: string = '';

  constructor(
    private router: Router,
    private route: ActivatedRoute,
    private authService: AuthService,
    private userDataService: UserDataService
  ) {
    this.route.queryParams.subscribe(params => {
      this.role = params['role'] || '';
    });
  }

  ngOnInit(): void {
    // Check if user is authenticated or has just registered
    if (!this.authService.isAuthenticated()) {
      // Check if there's a registered user (they might have just registered)
      const registeredUser = localStorage.getItem('registeredUser');
      if (!registeredUser) {
        // No registered user, redirect to login
        this.router.navigate(['/login'], { queryParams: { role: this.role } });
      }
      // If there's a registered user, allow them to continue with profile completion
    }
  }

  onSubmit(): void {
    if (this.supInfoData.birthday && this.supInfoData.gender && this.supInfoData.phoneNumber) {
      // Validate phone number (should have at least 8 digits after +216)
      const phoneWithoutCountryCode = this.supInfoData.phoneNumber.replace('+216', '');
      if (phoneWithoutCountryCode.length < 8 || !/^\d+$/.test(phoneWithoutCountryCode)) {
        this.errorMessage = 'Please enter a valid phone number (minimum 8 digits).';
        this.successMessage = '';
        return;
      }

      // Update user with supplementary information
      let currentUser = this.authService.currentUser();

      // If no current user, try to get from registered user data
      if (!currentUser) {
        const registeredUserData = localStorage.getItem('registeredUser');
        if (registeredUserData) {
          const userData = JSON.parse(registeredUserData);
          currentUser = {
            full_name: userData.full_name,
            email: userData.email,
            avatar: userData.avatar,
            is_initialized: userData.is_initialized,
            role: userData.role
          };
        }
      }

      if (currentUser) {
        const updatedUser = {
          ...currentUser,
          birthday: this.supInfoData.birthday,
          gender: this.supInfoData.gender,
          phoneNumber: this.supInfoData.phoneNumber
        };

        // Update both AuthService and UserDataService
        this.authService.updateUser(updatedUser).subscribe({
          next: (user) => {
            console.log('Supplementary info saved successfully:', user);
            
            // Also update UserDataService with complete profile data
            this.userDataService.updateProfile({
              fullName: user.full_name,
              email: user.email,
              birthday: this.supInfoData.birthday,
              gender: this.supInfoData.gender,
              phoneNumber: this.supInfoData.phoneNumber,
              avatar: user.avatar,
              hasCustomAvatar: user.hasCustomAvatar || false,
              avatarPositionY: user.avatarPositionY || 0
            }).subscribe({
              next: () => {
                console.log('UserDataService updated successfully');
                this.errorMessage = '';
                this.successMessage = 'Information saved successfully! Redirecting to login...';

                // Store the updated user data in localStorage
                localStorage.setItem('userProfile', JSON.stringify({
                  fullName: user.full_name,
                  email: user.email,
                  birthday: this.supInfoData.birthday,
                  gender: this.supInfoData.gender,
                  phoneNumber: this.supInfoData.phoneNumber,
                  avatar: user.avatar,
                  hasCustomAvatar: user.hasCustomAvatar || false,
                  avatarPositionY: user.avatarPositionY || 0
                }));

                // Navigate to login page after 2 seconds so user can login with complete profile
                setTimeout(() => {
                  this.router.navigate(['/login'], { queryParams: { role: this.role } });
                }, 2000);
              },
              error: (err) => {
                console.error('Error updating UserDataService:', err);
                this.successMessage = '';
                this.errorMessage = 'Failed to save information. Please try again.';
              }
            });
          },
          error: (err) => {
            console.error('Error saving supplementary info:', err);
            this.successMessage = '';
            this.errorMessage = 'Failed to save information. Please try again.';
          }
        });
      } else {
        this.errorMessage = 'User session expired. Please create an account again.';
        this.successMessage = '';
      }
    } else {
      this.errorMessage = 'Please fill in all required fields.';
      this.successMessage = '';
    }
  }

  onPhoneInput(event: any): void {
    let value = event.target.value;

    // Ensure it always starts with +216
    if (!value.startsWith('+216')) {
      value = '+216';
    }

    // Remove any non-digit characters after +216
    const countryCode = '+216';
    const phoneNumber = value.substring(4).replace(/\D/g, '');

    this.supInfoData.phoneNumber = countryCode + phoneNumber;
    event.target.value = this.supInfoData.phoneNumber;
  }

  onPhoneKeydown(event: KeyboardEvent): void {
    // Prevent deletion of +216
    const input = event.target as HTMLInputElement;
    if ((event.key === 'Backspace' || event.key === 'Delete') && input.selectionStart! <= 4) {
      event.preventDefault();
    }
  }

  getMaxDate(): string {
    return new Date().toISOString().split('T')[0];
  }

  onSkip(): void {
    // Skip profile completion and go directly to login
    this.router.navigate(['/login'], { queryParams: { role: this.role } });
  }
}
