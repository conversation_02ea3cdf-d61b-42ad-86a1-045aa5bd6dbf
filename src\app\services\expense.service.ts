import { Injectable } from '@angular/core';
import { Observable, of } from 'rxjs';
import { ShopperAnalyticsData } from '../models/expense.model';

@Injectable({
  providedIn: 'root'
})
export class ExpenseService {
  getAnalyticsData(): Observable<ShopperAnalyticsData> {
    const mockData: ShopperAnalyticsData = {
      budgetProgress: { savingsGoalPercentage: 75, budgetSpentPercentage: 60 },
      monthlySpending: { amount: 1200, percentageChange: 5, trendData: [1000, 1100, 1200] },
      expensesByCategory: [{ name: 'Food', amount: 400 }, { name: 'Transport', amount: 200 }],
      expensesByProduct: [{ name: 'Coffee', amount: 50 }, { name: 'Fuel', amount: 100 }],
      summaryValue: 'Total Expenses: 1200 TND'
    };
    return of(mockData);
  }
}