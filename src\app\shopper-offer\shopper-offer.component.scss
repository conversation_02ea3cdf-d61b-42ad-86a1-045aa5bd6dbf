// Variables
$primary-color: #ff6b9b; // Pink primary color
$primary-dark: darken($primary-color, 10%);
$primary-light: lighten($primary-color, 10%);
$accent-color: #6B48FF; // Purple accent
$text-dark: #2d3748;
$text-light: #718096;
$card-shadow: 0 10px 30px rgba(0, 0, 0, 0.1), 0 1px 8px rgba(0, 0, 0, 0.06);
$card-shadow-hover: 0 20px 40px rgba(0, 0, 0, 0.15), 0 1px 10px rgba(0, 0, 0, 0.1);
$border-radius: 16px;
$transition-fast: 0.2s ease;
$transition-medium: 0.3s ease;

// Container and Layout
.offers-container {
  padding: 30px;
  margin-left: 250px;
  margin-top: 64px;
  height: auto; /* Use auto height instead of min-height */
  max-height: calc(100vh - 64px); /* Set a maximum height */
  background-color: var(--primary-bg, #f9f9f9);
  transition: background-color $transition-fast, margin-left $transition-fast;
  will-change: background-color, margin-left;
  overflow-y: auto; /* Enable vertical scrolling only when needed */
  scrollbar-width: none; /* Hide scrollbar for Firefox */

  /* Hide scrollbar for Webkit browsers */
  &::-webkit-scrollbar {
    display: none;
  }

  /* Only show scrollbar when content overflows */
  &.has-overflow {
    scrollbar-width: thin; /* For Firefox */

    &::-webkit-scrollbar {
      display: block;
      width: 8px;
      background-color: transparent;
    }

    &::-webkit-scrollbar-thumb {
      background-color: rgba(0, 0, 0, 0.2);
      border-radius: 4px;
    }

    /* Only show scrollbar when hovering over the container */
    &:not(:hover)::-webkit-scrollbar-thumb {
      background-color: transparent;
    }

    /* Dark mode scrollbar styles */
    :host-context([data-theme="dark"]) & {
      &::-webkit-scrollbar-thumb {
        background-color: rgba(255, 255, 255, 0.2);
      }
    }
  }
}

.offers-header {
  padding: 20px 0;
  margin-bottom: 20px;

  h1 {
    font-size: 32px;
    font-weight: 700;
    color: var(--text-color, $text-dark);
    margin: 0;
    position: relative;
    display: inline-block;

    &:after {
      content: '';
      position: absolute;
      bottom: -8px;
      left: 0;
      width: 60px;
      height: 4px;
      background: $primary-color;
      border-radius: 2px;
    }
  }
}

.offers-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(340px, 1fr));
  gap: 30px;
  padding: 16px 0;
  overflow: visible; /* Ensure grid items can be seen even when they overflow */
  min-height: 0; /* Prevent grid from expanding unnecessarily */
}

.offer-wrapper {
  display: flex;
  justify-content: center;
}

// Card Design
.offer-card {
  background: var(--card-bg, white);
  border-radius: $border-radius;
  overflow: hidden;
  box-shadow: $card-shadow;
  transition: transform $transition-medium, box-shadow $transition-medium, background-color $transition-fast;
  width: 340px;
  position: relative;
  will-change: transform, box-shadow, background-color;
  transform: translateZ(0);
  display: flex;
  flex-direction: column;
  border: none;
}

.offer-card:hover {
  transform: translateY(-8px) scale(1.02) translateZ(0);
  box-shadow: $card-shadow-hover;

  .offer-image img {
    transform: scale(1.05);
  }
}

// Product Badge
.product-badge {
  position: absolute;
  top: 16px;
  left: 16px;
  z-index: 10;
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(4px);
  padding: 8px 16px;
  border-radius: 30px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);

  span {
    font-size: 14px;
    font-weight: 600;
    color: $primary-color;
  }
}

// Image Slider
.image-slider-container {
  position: relative;
  width: 100%;
  height: 260px;
  overflow: hidden;
}

.offer-image {
  width: 100%;
  height: 100%;
  overflow: hidden;

  img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.5s ease;
  }
}

// Brand Section
.brand-section {
  padding: 20px;
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}

.profile {
  display: flex;
  align-items: center;
  gap: 16px;

  .avatar-container {
    position: relative;
    width: 60px;
    height: 60px;
    border-radius: 16px;
    overflow: hidden;
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
    border: 3px solid white;
  }

  &-avatar {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform $transition-fast;

    &:hover {
      transform: scale(1.05);
    }
  }

  &-info {
    h3 {
      font-size: 18px;
      font-weight: 700;
      color: var(--text-color, $text-dark);
      margin: 0 0 6px 0;
    }

    .date {
      font-size: 14px;
      color: var(--text-color, $text-light);
      opacity: 0.9;
      display: flex;
      align-items: center;

      &:before {
        content: '\f073'; // Calendar icon
        font-family: 'Font Awesome 5 Free';
        font-weight: 900;
        margin-right: 6px;
        font-size: 12px;
        opacity: 0.7;
      }
    }
  }
}

// Tags Section
.tags {
  padding: 16px 20px;
  display: flex;
  flex-wrap: wrap;
  gap: 8px;

  .tag {
    font-size: 13px;
    font-weight: 500;
    color: $accent-color;
    background-color: rgba($accent-color, 0.08);
    padding: 4px 12px;
    border-radius: 20px;
    transition: all $transition-fast;

    &:hover {
      background-color: rgba($accent-color, 0.15);
      transform: translateY(-2px);
    }
  }
}

// Price and Buy Section
.price-buy-section {
  padding: 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  background-color: rgba(0, 0, 0, 0.02);
  border-top: 1px solid rgba(0, 0, 0, 0.05);
  border-radius: 0 0 $border-radius $border-radius;
}

.price-container {
  display: flex;
  flex-direction: column;

  .price-label {
    font-size: 13px;
    color: $text-light;
    margin-bottom: 4px;
  }

  .price-value {
    font-size: 22px;
    font-weight: 700;
    color: var(--text-color, $text-dark);

    .currency {
      font-size: 16px;
      font-weight: 600;
      opacity: 0.8;
    }
  }
}

.buy-now-btn {
  background: linear-gradient(135deg, $primary-color, $primary-dark);
  color: white;
  border: none;
  border-radius: 30px;
  padding: 12px 24px;
  font-size: 15px;
  font-weight: 600;
  cursor: pointer;
  transition: all $transition-medium;
  box-shadow: 0 4px 15px rgba($primary-color, 0.3);
  display: flex;
  align-items: center;
  gap: 8px;

  i {
    font-size: 14px;
  }

  &:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 20px rgba($primary-color, 0.4);
    background: linear-gradient(135deg, $primary-light, $primary-color);
  }

  &:active {
    transform: translateY(-1px);
    box-shadow: 0 4px 10px rgba($primary-color, 0.3);
  }
}

// Favorite Button Overlay
.favorite-btn-overlay {
  position: absolute;
  top: 16px;
  right: 16px;
  background: rgba(255, 255, 255, 0.9);
  border: none;
  border-radius: 50%;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
  transition: all $transition-fast;
  z-index: 10;

  i {
    font-size: 18px;
    color: $accent-color;
    transition: all $transition-fast;

    &.fa-heart {
      color: #ff4757;
    }
  }

  &:hover {
    transform: scale(1.1);
    background: white;
    box-shadow: 0 6px 15px rgba(0, 0, 0, 0.15);

    i {
      transform: scale(1.1);
    }
  }
}

// Swiper Navigation
.swiper-pagination {
  bottom: 10px !important;
}

.swiper-button-prev,
.swiper-button-next {
  color: white !important;
  width: 36px !important;
  height: 36px !important;
  background: rgba(0, 0, 0, 0.3);
  border-radius: 50%;
  transition: all $transition-fast;

  &:after {
    font-size: 16px !important;
  }

  &:hover {
    background: rgba(0, 0, 0, 0.5);
    transform: scale(1.1);
  }
}

.swiper-button-prev {
  left: 15px !important;
}

.swiper-button-next {
  right: 15px !important;
}

.swiper-pagination-bullet {
  background: white !important;
  opacity: 0.7 !important;

  &-active {
    opacity: 1 !important;
    background: $primary-color !important;
  }
}

// Responsive Design
@media (max-width: 1200px) {
  .offers-grid {
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  }

  .offer-card {
    width: 300px;
  }
}

@media (max-width: 768px) {
  .offers-container {
    margin-left: 0;
    padding: 20px;
    transition: margin-left $transition-fast, padding $transition-fast;
  }

  .offers-header h1 {
    font-size: 28px;
  }

  .offers-grid {
    grid-template-columns: 1fr;
    gap: 24px;
    transition: grid-template-columns $transition-fast;
  }

  .offer-card {
    width: 100%;
    max-width: 340px;
    transition: width $transition-fast, max-width $transition-fast;
  }

  .image-slider-container {
    height: 220px;
  }

  .profile {
    .avatar-container {
      width: 50px;
      height: 50px;
    }

    &-info h3 {
      font-size: 16px;
    }
  }

  .price-container .price-value {
    font-size: 20px;
  }

  .buy-now-btn {
    padding: 10px 20px;
    font-size: 14px;
  }
}

@media (max-width: 480px) {
  .offers-container {
    padding: 16px;
  }

  .offers-header h1 {
    font-size: 24px;
  }

  .image-slider-container {
    height: 200px;
  }

  .brand-section,
  .tags,
  .price-buy-section {
    padding: 15px;
  }

  .price-container .price-value {
    font-size: 18px;
  }

  .buy-now-btn {
    padding: 8px 16px;
    font-size: 13px;
  }
}