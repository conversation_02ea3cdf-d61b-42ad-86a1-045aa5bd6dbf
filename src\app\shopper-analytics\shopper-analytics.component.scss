// shopper-analytics.component.scss
// Color variables
$primary-color: #667eea;
$primary-light: #c3dafe;
$primary-dark: #4c51bf;
$danger-color: #e53e3e;
$success-color: #38a169;
$warning-color: #ed8936;
$text-primary: var(--text-color, #2d3748);
$text-secondary: var(--text-color, #718096);
$text-light: var(--text-color, #a0aec0);
$border-color: var(--border-color, #e2e8f0);
$bg-light: var(--card-bg, #ffffff);
$bg-lighter: var(--primary-bg, #f7fafc);
$bg-accent: var(--secondary-bg, #ebf4ff);
$input-bg: var(--input-bg, #ffffff);
$input-text: var(--input-text, #2d3748);
$input-border: var(--input-border, #e2e8f0);

// Shadows
$shadow-sm: 0 1px 3px rgba(0, 0, 0, 0.1), 0 1px 2px rgba(0, 0, 0, 0.06);
$shadow-md: 0 4px 6px rgba(0, 0, 0, 0.05), 0 2px 4px rgba(0, 0, 0, 0.06);
$shadow-lg: 0 10px 15px rgba(0, 0, 0, 0.05), 0 4px 6px rgba(0, 0, 0, 0.05);
$shadow-card: 0 0 0 1px rgba(0, 0, 0, 0.05), 0 5px 15px rgba(0, 0, 0, 0.05);

// Sizing and spacing
$border-radius-sm: 6px;
$border-radius-md: 10px;
$border-radius-lg: 16px;
$border-radius-xl: 20px;
$sidebar-width: 240px;
$navbar-height: 70px;

// Transitions
$transition-fast: 150ms ease;
$transition-normal: 250ms ease;
$transition-slow: 350ms cubic-bezier(0.4, 0, 0.2, 1);

// Responsive breakpoints
@mixin desktop {
  @media (min-width: 1280px) {
    @content;
  }
}

@mixin laptop {
  @media (min-width: 1024px) and (max-width: 1279px) {
    @content;
  }
}

@mixin tablet {
  @media (min-width: 768px) and (max-width: 1023px) {
    @content;
  }
}

@mixin mobile {
  @media (max-width: 767px) {
    @content;
  }
}

@mixin small-mobile {
  @media (max-width: 575px) {
    @content;
  }
}

// Mixins
@mixin card {
  background-color: $bg-light;
  border-radius: $border-radius-lg;
  box-shadow: $shadow-card;
  transition: box-shadow $transition-normal, transform $transition-normal;
  overflow: hidden;

  &:hover {
    box-shadow: $shadow-lg;
  }
}

@mixin section-padding {
  padding: 24px;

  @include tablet {
    padding: 20px;
  }

  @include mobile {
    padding: 16px;
  }
}

// Main container layout
.analytics-container {
  margin-left: $sidebar-width;
  padding: 30px;
  padding-top: calc(#{$navbar-height} + 24px);
  min-height: 100vh;
  background-color: $bg-lighter;
  display: flex;
  justify-content: center;
  transition: margin-left $transition-normal;

  @include tablet {
    margin-left: 80px; // Collapsed sidebar width
    padding: 24px;
    padding-top: calc(#{$navbar-height} + 20px);
  }

  @include mobile {
    margin-left: 0;
    padding: 16px;
    padding-top: calc(#{$navbar-height} + 16px);
  }
}

// Analytics card
.analytics-card {
  @include card;
  width: 100%;
  max-width: 1100px;
  @include section-padding;
  position: relative;

  // Dashboard header
  .dashboard-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 24px;

    .dashboard-title {
      font-size: 1.5rem;
      font-weight: 700;
      color: $text-primary;

      @include mobile {
        font-size: 1.25rem;
      }
    }

    .date-range {
      display: flex;
      align-items: center;
      background-color: $input-bg;
      border: 1px solid $input-border;
      border-radius: $border-radius-md;
      padding: 8px 16px;
      font-size: 0.9rem;
      color: $input-text;

      .icon {
        margin-right: 8px;
        color: $primary-color;
      }
    }
  }
}

// Progress section
.progress-section {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 24px;
  margin-bottom: 32px;

  @include tablet {
    grid-template-columns: repeat(auto-fit, minmax(240px, 1fr));
    gap: 20px;
  }

  @include mobile {
    grid-template-columns: 1fr;
    gap: 16px;
  }
}

// Progress item styling
.progress-item {
  background-color: $bg-light;
  border-radius: $border-radius-md;
  border: 1px solid $border-color;
  padding: 24px;
  box-shadow: $shadow-sm;
  transition: transform $transition-normal, box-shadow $transition-normal;

  &:hover {
    transform: translateY(-4px);
    box-shadow: $shadow-md;
  }

  @include mobile {
    padding: 20px;
  }

  // Add some visual distinction between items
  &:nth-child(1) {
    border-top: 3px solid $primary-color;
  }

  &:nth-child(2) {
    border-top: 3px solid $warning-color;
  }
}

// Divider
.divider {
  height: 1px;
  background: linear-gradient(to right, transparent, $border-color, transparent);
  margin: 32px 0;

  @include mobile {
    margin: 24px 0;
  }
}

// Spending section
.spending-section {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background-color: $bg-accent;
  border-radius: $border-radius-md;
  padding: 24px;

  @include mobile {
    flex-direction: column;
    padding: 20px;
  }
}

// Spending info
.spending-info {
  flex: 1;

  @include mobile {
    width: 100%;
    text-align: center;
    margin-bottom: 20px;
  }
}

// Spending amount
.spending-amount {
  font-size: 2.5rem;
  font-weight: 700;
  color: $text-primary;
  letter-spacing: -0.02em;
  display: flex;
  align-items: baseline;

  @include mobile {
    font-size: 2rem;
    justify-content: center;
  }

  .currency {
    color: $text-secondary;
    font-size: 0.5em;
    margin-left: 4px;
    font-weight: 600;
  }
}

// Spending change
.spending-change {
  margin: 12px 0;
  font-size: 1.1rem;
  display: flex;
  align-items: center;

  @include mobile {
    justify-content: center;
  }

  .percentage {
    display: inline-flex;
    align-items: center;
    font-weight: 600;
    padding: 4px 10px;
    border-radius: 16px;

    &.increase {
      color: $danger-color;
      background-color: rgba($danger-color, 0.1);

      &::before {
        content: "↑";
        margin-right: 4px;
      }
    }

    &.decrease {
      color: $success-color;
      background-color: rgba($success-color, 0.1);

      &::before {
        content: "↓";
        margin-right: 4px;
      }
    }
  }

  .compared-period {
    margin-left: 10px;
    color: $text-light;
    font-size: 0.9rem;
  }
}

// Spending label
.spending-label {
  color: $text-secondary;
  font-size: 1rem;
  margin-top: 8px;
  font-weight: 500;
}

// Trend chart container
.trend-chart-container {
  width: 180px;
  height: 80px;
  padding: 8px;
  background-color: rgba(var(--input-bg, 255, 255, 255), 0.8);
  border-radius: $border-radius-sm;

  @include tablet {
    width: 160px;
    height: 70px;
  }

  @include mobile {
    width: 100%;
    height: 120px;
  }
}

// Expense list sections
app-expense-list {
  margin-bottom: 32px;
  display: block;

  &:last-of-type {
    margin-bottom: 16px;
  }

  // Add style for section headers
  ::ng-deep .expense-section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;

    h3 {
      font-size: 1.1rem;
      font-weight: 600;
      color: $text-primary;
    }

    .toggle-btn {
      background: none;
      border: none;
      color: $primary-color;
      cursor: pointer;
      font-size: 0.9rem;
      display: flex;
      align-items: center;

      &:hover {
        text-decoration: underline;
      }

      .icon {
        margin-left: 4px;
      }
    }
  }
}

// Summary value
.summary-value {
  text-align: center;
  font-size: 1.5rem;
  font-weight: 600;
  color: $text-primary;
  margin-top: 32px;
  padding: 16px;
  background-color: $bg-lighter;
  border-radius: $border-radius-md;

  @include mobile {
    font-size: 1.25rem;
    margin-top: 24px;
    padding: 12px;
  }
}

// Loading container
.loading-container {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba($bg-lighter, 0.8);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  backdrop-filter: blur(3px);

  .loading-spinner {
    width: 50px;
    height: 50px;
    border: 3px solid rgba($primary-color, 0.1);
    border-top-color: $primary-color;
    border-radius: 50%;
    animation: spinner 1s linear infinite;
  }

  div {
    margin-top: 16px;
    color: $text-secondary;
    font-weight: 500;
    font-size: 1.1rem;
  }
}

// Animations
@keyframes spinner {
  to {
    transform: rotate(360deg);
  }
}

// Fade in animation
.fade-in {
  animation: fadeIn 0.4s ease-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

// Dashboard card
.dashboard-card {
  @include card;
  padding: 20px;
  margin-bottom: 24px;

  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;

    .card-title {
      font-weight: 600;
      color: $text-primary;
      font-size: 1.1rem;
    }
  }

  @include mobile {
    padding: 16px;
    margin-bottom: 16px;
  }
}

// Stats grid
.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
  margin-bottom: 24px;

  .stat-item {
    background-color: $bg-light;
    border-radius: $border-radius-md;
    padding: 16px;
    border: 1px solid $border-color;
    transition: transform $transition-fast;

    &:hover {
      transform: translateY(-2px);
    }

    .stat-label {
      color: $text-secondary;
      font-size: 0.9rem;
      margin-bottom: 8px;
    }

    .stat-value {
      font-size: 1.5rem;
      font-weight: 600;
      color: $text-primary;
    }

    .stat-change {
      margin-top: 8px;
      font-size: 0.85rem;
      display: flex;
      align-items: center;

      &.positive {
        color: $success-color;
      }

      &.negative {
        color: $danger-color;
      }

      .icon {
        margin-right: 4px;
      }
    }
  }

  @include mobile {
    grid-template-columns: 1fr;
    gap: 12px;
  }
}

// Data cards for customization
.data-card {
  background-color: $bg-light;
  border-radius: $border-radius-md;
  box-shadow: $shadow-sm;
  padding: 20px;
  margin-bottom: 20px;
  border-left: 4px solid transparent;

  &.primary {
    border-left-color: $primary-color;
  }

  &.success {
    border-left-color: $success-color;
  }

  &.warning {
    border-left-color: $warning-color;
  }

  &.danger {
    border-left-color: $danger-color;
  }

  .data-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 12px;

    .data-title {
      font-weight: 600;
      color: $text-primary;
      display: flex;
      align-items: center;

      .icon {
        margin-right: 8px;
      }
    }
  }

  @include mobile {
    padding: 16px;
  }
}