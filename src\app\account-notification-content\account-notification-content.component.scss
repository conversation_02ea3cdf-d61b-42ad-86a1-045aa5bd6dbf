.notification-content-container {
  width: 100%;
  height: 100%;
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

// Notification List View
.notification-list-view {
  display: flex;
  flex-direction: column;
  height: 100%;
}

.notification-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 24px;
  border-bottom: 1px solid #e5e7eb;
  background: #f9fafb;

  .header-title {
    display: flex;
    align-items: center;
    gap: 12px;

    h3 {
      margin: 0;
      font-size: 1.25rem;
      font-weight: 600;
      color: #111827;
    }

    .unread-badge {
      background: #ef4444;
      color: white;
      border-radius: 12px;
      padding: 2px 8px;
      font-size: 0.75rem;
      font-weight: 600;
      min-width: 20px;
      text-align: center;
    }
  }

  .header-actions {
    .mark-all-read-btn {
      background: #6366f1;
      color: white;
      border: none;
      border-radius: 8px;
      padding: 8px 12px;
      cursor: pointer;
      transition: background 0.2s;

      &:hover {
        background: #4f46e5;
      }

      i {
        font-size: 0.875rem;
      }
    }
  }
}

.urgent-alert {
  background: #fef3c7;
  border: 1px solid #f59e0b;
  color: #92400e;
  padding: 12px 24px;
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 500;

  i {
    color: #f59e0b;
  }
}

.notifications-list {
  flex: 1;
  overflow-y: auto;
  padding: 0;
}

.notification-item {
  display: flex;
  align-items: flex-start;
  padding: 16px 24px;
  border-bottom: 1px solid #f3f4f6;
  cursor: pointer;
  transition: all 0.2s;
  position: relative;

  &:hover {
    background: #f9fafb;
  }

  &.unread {
    background: #fefefe;
    border-left: 4px solid #6366f1;

    .notification-title {
      font-weight: 600;
    }
  }

  &.priority-urgent {
    border-left-color: #ef4444;

    &.unread {
      background: #fef2f2;
    }
  }

  &.priority-high {
    border-left-color: #f59e0b;
  }

  &.priority-medium {
    border-left-color: #6366f1;
  }

  &.priority-low {
    border-left-color: #10b981;
  }

  .notification-icon {
    flex-shrink: 0;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: #f3f4f6;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 12px;

    i {
      font-size: 1.125rem;
    }
  }

  .notification-content {
    flex: 1;
    min-width: 0;

    .notification-title {
      font-size: 0.875rem;
      font-weight: 500;
      color: #111827;
      margin-bottom: 4px;
      line-height: 1.4;
    }

    .notification-message {
      font-size: 0.8rem;
      color: #6b7280;
      margin-bottom: 8px;
      line-height: 1.4;
      display: -webkit-box;
      -webkit-line-clamp: 2;
      -webkit-box-orient: vertical;
      overflow: hidden;
    }

    .notification-meta {
      display: flex;
      gap: 12px;
      font-size: 0.75rem;
      color: #9ca3af;

      .notification-time {
        font-weight: 500;
      }

      .notification-category {
        text-transform: capitalize;
        background: #f3f4f6;
        padding: 2px 6px;
        border-radius: 4px;
      }
    }
  }

  .notification-actions {
    display: flex;
    gap: 4px;
    opacity: 0;
    transition: opacity 0.2s;

    .action-btn {
      background: none;
      border: none;
      padding: 6px;
      border-radius: 4px;
      cursor: pointer;
      transition: background 0.2s;

      &.mark-read-btn {
        color: #10b981;

        &:hover {
          background: #ecfdf5;
        }
      }

      &.delete-btn {
        color: #ef4444;

        &:hover {
          background: #fef2f2;
        }
      }

      i {
        font-size: 0.75rem;
      }
    }
  }

  &:hover .notification-actions {
    opacity: 1;
  }

  .unread-indicator {
    position: absolute;
    right: 16px;
    top: 50%;
    transform: translateY(-50%);
    width: 8px;
    height: 8px;
    background: #6366f1;
    border-radius: 50%;
  }
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 24px;
  text-align: center;
  color: #6b7280;

  i {
    font-size: 3rem;
    margin-bottom: 16px;
    color: #d1d5db;
  }

  h4 {
    margin: 0 0 8px 0;
    font-size: 1.125rem;
    font-weight: 600;
    color: #374151;
  }

  p {
    margin: 0;
    font-size: 0.875rem;
    line-height: 1.5;
  }
}

// Notification Detail View
.notification-detail-view {
  display: flex;
  flex-direction: column;
  height: 100%;
}

.detail-header {
  padding: 16px 24px;
  border-bottom: 1px solid #e5e7eb;
  background: #f9fafb;

  .back-btn {
    background: none;
    border: none;
    color: #6366f1;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 0.875rem;
    font-weight: 500;
    transition: color 0.2s;

    &:hover {
      color: #4f46e5;
    }

    i {
      font-size: 0.75rem;
    }
  }
}

.detail-content {
  flex: 1;
  overflow-y: auto;
  padding: 24px;
}

.detail-notification-header {
  display: flex;
  align-items: flex-start;
  gap: 16px;
  margin-bottom: 24px;

  .detail-icon {
    flex-shrink: 0;
    width: 56px;
    height: 56px;
    border-radius: 50%;
    background: #f3f4f6;
    display: flex;
    align-items: center;
    justify-content: center;

    i {
      font-size: 1.5rem;
    }
  }

  .detail-title-section {
    flex: 1;

    h2 {
      margin: 0 0 8px 0;
      font-size: 1.5rem;
      font-weight: 600;
      color: #111827;
      line-height: 1.3;
    }

    .detail-meta {
      display: flex;
      gap: 16px;
      align-items: center;
      font-size: 0.875rem;

      .detail-time {
        color: #6b7280;
        font-weight: 500;
      }

      .detail-category {
        background: #f3f4f6;
        color: #374151;
        padding: 4px 8px;
        border-radius: 6px;
        text-transform: capitalize;
        font-weight: 500;
      }

      .detail-priority {
        padding: 4px 8px;
        border-radius: 6px;
        font-weight: 600;
        text-transform: uppercase;
        font-size: 0.75rem;

        &.priority-urgent {
          background: #fef2f2;
          color: #dc2626;
        }

        &.priority-high {
          background: #fef3c7;
          color: #d97706;
        }

        &.priority-medium {
          background: #ede9fe;
          color: #7c3aed;
        }

        &.priority-low {
          background: #ecfdf5;
          color: #059669;
        }
      }
    }
  }
}

.detail-message {
  margin-bottom: 24px;

  p {
    font-size: 1rem;
    line-height: 1.6;
    color: #374151;
    margin: 0;
  }
}

.detail-content-body {
  background: #f9fafb;
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 24px;

  div {
    font-size: 0.875rem;
    line-height: 1.6;
    color: #374151;

    p {
      margin-bottom: 12px;

      &:last-child {
        margin-bottom: 0;
      }
    }

    ul, ol {
      margin: 12px 0;
      padding-left: 20px;

      li {
        margin-bottom: 4px;
      }
    }

    strong {
      font-weight: 600;
      color: #111827;
    }

    em {
      font-style: italic;
      color: #6b7280;
    }
  }
}

.detail-actions {
  .action-button {
    display: inline-flex;
    align-items: center;
    gap: 8px;
    background: #6366f1;
    color: white;
    text-decoration: none;
    padding: 12px 20px;
    border-radius: 8px;
    font-weight: 500;
    transition: background 0.2s;

    &:hover {
      background: #4f46e5;
    }

    i {
      font-size: 0.875rem;
    }
  }
}

// Dark mode support
@media (prefers-color-scheme: dark) {
  .notification-content-container {
    background: #1f2937;
    color: #f9fafb;
  }

  .notification-header {
    background: #111827;
    border-bottom-color: #374151;

    h3 {
      color: #f9fafb;
    }
  }

  .notification-item {
    border-bottom-color: #374151;

    &:hover {
      background: #374151;
    }

    &.unread {
      background: #1f2937;
    }

    .notification-title {
      color: #f9fafb;
    }

    .notification-message {
      color: #d1d5db;
    }

    .notification-meta {
      color: #9ca3af;

      .notification-category {
        background: #374151;
        color: #d1d5db;
      }
    }

    .notification-icon {
      background: #374151;
    }
  }

  .detail-header {
    background: #111827;
    border-bottom-color: #374151;
  }

  .detail-notification-header {
    .detail-title-section h2 {
      color: #f9fafb;
    }

    .detail-icon {
      background: #374151;
    }
  }

  .detail-message p {
    color: #d1d5db;
  }

  .detail-content-body {
    background: #111827;

    div {
      color: #d1d5db;

      strong {
        color: #f9fafb;
      }
    }
  }

  .empty-state {
    color: #9ca3af;

    h4 {
      color: #d1d5db;
    }

    i {
      color: #6b7280;
    }
  }
}

// Responsive design
@media (max-width: 768px) {
  .notification-header {
    padding: 16px;

    .header-title h3 {
      font-size: 1.125rem;
    }
  }

  .notification-item {
    padding: 12px 16px;

    .notification-icon {
      width: 36px;
      height: 36px;

      i {
        font-size: 1rem;
      }
    }
  }

  .detail-header {
    padding: 12px 16px;
  }

  .detail-content {
    padding: 16px;
  }

  .detail-notification-header {
    .detail-icon {
      width: 48px;
      height: 48px;

      i {
        font-size: 1.25rem;
      }
    }

    .detail-title-section h2 {
      font-size: 1.25rem;
    }
  }
}