import { Injectable } from '@angular/core';
import { BehaviorSubject, Observable } from 'rxjs';
import { Router } from '@angular/router';

// Interface for notifications
export interface Notification {
  id: string;
  title: string;
  message: string;
  time: Date;
  read: boolean;
  detailContent?: string; // Optional detailed content to show when notification is selected
}

export interface NotificationSetting {
  id: string;
  title: string;
  description: string;
  enabled: boolean;
}

export interface NotificationSettings {
  brandOffers: NotificationSetting;
  marketingNotifications: NotificationSetting;
  newPartners: NotificationSetting;
  appUpdates: NotificationSetting;
}

@Injectable({
  providedIn: 'root'
})
export class NotificationService {
  private defaultSettings: NotificationSettings = {
    brandOffers: {
      id: 'brandOffers',
      title: 'Brand Offers Notifications',
      description: 'Offer Based On Your Shopping History',
      enabled: true
    },
    marketingNotifications: {
      id: 'marketingNotifications',
      title: 'Marketing Notifications',
      description: 'Receeto Notification About Our Offers',
      enabled: true
    },
    newPartners: {
      id: 'newPartners',
      title: 'New Partners Notifications',
      description: 'Get Informed About Every New Shopper',
      enabled: true
    },
    appUpdates: {
      id: 'appUpdates',
      title: 'App Updates',
      description: 'Get Informed About Our New Features',
      enabled: true
    }
  };

  private notificationSettingsSubject = new BehaviorSubject<NotificationSettings>(this.defaultSettings);
  notificationSettings$ = this.notificationSettingsSubject.asObservable();

  // Track current user email for user-specific settings
  private currentUserEmail: string | null = null;

  // Always start with notifications panel hidden
  private showNotificationsSubject = new BehaviorSubject<boolean>(false);
  showNotifications$ = this.showNotificationsSubject.asObservable();

  // User-specific notifications - properly isolated per user
  private notificationsSubject = new BehaviorSubject<Notification[]>([]);
  notifications$ = this.notificationsSubject.asObservable();

  constructor(private router: Router) {
    // Initialize with empty notifications (user-specific notifications will be loaded when user logs in)
    this.notificationsSubject.next([]);

    // Initialize with default settings (user-specific settings will be loaded when user logs in)
    this.notificationSettingsSubject.next(this.defaultSettings);
  }

  /**
   * Clear all notifications for current user
   */
  private clearNotifications(): void {
    // Reset to empty array
    this.notificationsSubject.next([]);

    // Clear from localStorage for current user only
    if (this.currentUserEmail) {
      const storageKey = `notifications_${this.currentUserEmail}`;
      localStorage.removeItem(storageKey);
    }
  }

  /**
   * Load user-specific notifications from localStorage
   */
  private loadUserNotifications(): void {
    if (!this.currentUserEmail) {
      this.notificationsSubject.next([]);
      return;
    }

    try {
      const storageKey = `notifications_${this.currentUserEmail}`;
      const savedNotifications = localStorage.getItem(storageKey);

      if (savedNotifications) {
        const userNotifications = JSON.parse(savedNotifications);
        this.notificationsSubject.next(userNotifications);
        console.log('Loaded notifications for user:', this.currentUserEmail);
      } else {
        // First time user - start with empty notifications
        this.notificationsSubject.next([]);
        console.log('No existing notifications for new user:', this.currentUserEmail);
      }
    } catch (error) {
      console.error('Error loading user notifications:', error);
      // Fallback to empty notifications
      this.notificationsSubject.next([]);
    }
  }

  toggleNotificationSetting(settingId: string): void {
    const currentSettings = this.notificationSettingsSubject.value;
    const updatedSettings = { ...currentSettings };

    // Type assertion to access the property dynamically
    const setting = updatedSettings[settingId as keyof NotificationSettings];
    if (setting) {
      setting.enabled = !setting.enabled;

      // Update the subject
      this.notificationSettingsSubject.next(updatedSettings);

      // Save user-specific settings to localStorage
      this.saveUserNotificationSettings(updatedSettings);
    }
  }

  toggleNotificationsPanel(): void {
    // If we're on the user profile page, just emit true to trigger the flash animation
    if (this.router.url === '/user-profile') {
      this.showNotificationsSubject.next(true);
      return;
    }

    // Otherwise toggle as normal
    this.showNotificationsSubject.next(!this.showNotificationsSubject.value);
  }

  hideNotificationsPanel(): void {
    this.showNotificationsSubject.next(false);
  }

  getNotificationSettings(): Observable<NotificationSettings> {
    return this.notificationSettings$;
  }

  getShowNotifications(): Observable<boolean> {
    return this.showNotifications$;
  }

  /**
   * Get all notifications
   */
  getNotifications(): Observable<Notification[]> {
    return this.notifications$;
  }

  /**
   * Add a new notification
   * @param notification The notification to add
   */
  addNotification(notification: Omit<Notification, 'id'>): void {
    const currentNotifications = this.notificationsSubject.value;

    // Generate a unique ID
    const newId = Date.now().toString();

    // Create the new notification with ID
    const newNotification: Notification = {
      ...notification,
      id: newId
    };

    // Add to the beginning of the array
    const updatedNotifications = [newNotification, ...currentNotifications];

    // Update the subject
    this.notificationsSubject.next(updatedNotifications);

    // Save to localStorage
    this.saveNotificationsToLocalStorage(updatedNotifications);

    // Show the notifications panel
    this.showNotificationsSubject.next(true);
  }

  /**
   * Mark a notification as read
   * @param notificationId The ID of the notification to mark as read
   */
  markNotificationAsRead(notificationId: string): void {
    const currentNotifications = this.notificationsSubject.value;

    // Find and update the notification
    const updatedNotifications = currentNotifications.map(notification => {
      if (notification.id === notificationId) {
        return { ...notification, read: true };
      }
      return notification;
    });

    // Update the subject
    this.notificationsSubject.next(updatedNotifications);

    // Save to localStorage
    this.saveNotificationsToLocalStorage(updatedNotifications);
  }

  /**
   * Save notifications to localStorage for current user
   * @param notifications The notifications to save
   */
  private saveNotificationsToLocalStorage(notifications: Notification[]): void {
    if (!this.currentUserEmail) {
      return;
    }

    try {
      const storageKey = `notifications_${this.currentUserEmail}`;
      localStorage.setItem(storageKey, JSON.stringify(notifications));
      console.log('Saved notifications for user:', this.currentUserEmail);
    } catch (e) {
      console.error('Error saving notifications to localStorage:', e);
    }
  }

  /**
   * Add a purchase notification
   * @param brandName The name of the brand
   * @param productName The name of the product
   * @param price The price of the product
   * @param quantity The quantity purchased
   */
  addPurchaseNotification(brandName: string, productName: string, price: number, quantity: number = 1): void {
    const quantityText = quantity > 1 ? `${quantity} x ` : '';
    this.addNotification({
      title: 'Purchase Successful',
      message: `You have successfully purchased ${quantityText}${productName} from ${brandName}.`,
      time: new Date(),
      read: false,
      detailContent: `Thank you for your purchase of ${quantityText}${productName} from ${brandName} for ${price.toFixed(2)} TND. Your transaction has been recorded and can be viewed in your transaction history.`
    });
  }

  /**
   * Clear all notifications - public method that can be called from other components
   */
  clearAllNotifications(): void {
    this.clearNotifications();
  }

  /**
   * Set current user for user-specific notification settings and notifications
   */
  setCurrentUser(userEmail: string): void {
    this.currentUserEmail = userEmail;
    this.loadUserNotificationSettings();
    this.loadUserNotifications();
  }

  /**
   * Clear current user (on logout)
   */
  clearCurrentUser(): void {
    this.currentUserEmail = null;
    // Reset to default settings and clear notifications
    this.notificationSettingsSubject.next(this.defaultSettings);
    this.notificationsSubject.next([]);
  }

  /**
   * Load user-specific notification settings from localStorage
   */
  private loadUserNotificationSettings(): void {
    if (!this.currentUserEmail) {
      return;
    }

    try {
      const storageKey = `notificationSettings_${this.currentUserEmail}`;
      const savedSettings = localStorage.getItem(storageKey);

      if (savedSettings) {
        const userSettings = JSON.parse(savedSettings);
        this.notificationSettingsSubject.next(userSettings);
        console.log('Loaded notification settings for user:', this.currentUserEmail);
      } else {
        // First time user - use default settings (all enabled) and save them
        this.notificationSettingsSubject.next(this.defaultSettings);
        this.saveUserNotificationSettings(this.defaultSettings);
        console.log('Created default notification settings for new user:', this.currentUserEmail);
      }
    } catch (error) {
      console.error('Error loading user notification settings:', error);
      // Fallback to default settings
      this.notificationSettingsSubject.next(this.defaultSettings);
    }
  }

  /**
   * Save user-specific notification settings to localStorage
   */
  private saveUserNotificationSettings(settings: NotificationSettings): void {
    if (!this.currentUserEmail) {
      return;
    }

    try {
      const storageKey = `notificationSettings_${this.currentUserEmail}`;
      localStorage.setItem(storageKey, JSON.stringify(settings));
      console.log('Saved notification settings for user:', this.currentUserEmail);
    } catch (error) {
      console.error('Error saving user notification settings:', error);
    }
  }

  /**
   * Get default notification settings (all enabled)
   */
  getDefaultSettings(): NotificationSettings {
    return { ...this.defaultSettings };
  }
}
