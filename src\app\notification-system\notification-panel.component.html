<!-- Keep the original class for user profile page -->
<div class="notification-panel" *ngIf="isUserProfilePage" [class.show]="showPanel || isUserProfilePage" [class.dark-mode]="isDarkMode" [style.display]="(showPanel || isUserProfilePage) ? 'block' : 'none'">
  <div class="notification-header">
    <h3>Notifications</h3>
  </div>
  <div class="notification-content">
    <div class="notification-Brand-Offers">
      <div class="notification-info">
        <h4>{{ notificationSettings.brandOffers.title }}</h4>
        <p>{{ notificationSettings.brandOffers.description }}</p>
      </div>
      <div class="toggle-switch"
           [class.active]="notificationSettings.brandOffers.enabled"
           (click)="toggleNotification('brandOffers')">
        <div class="toggle-slider"></div>
      </div>
    </div>

    <div class="notification-Marketing">
      <div class="notification-info">
        <h4>{{ notificationSettings.marketingNotifications.title }}</h4>
        <p>{{ notificationSettings.marketingNotifications.description }}</p>
      </div>
      <div class="toggle-switch"
           [class.active]="notificationSettings.marketingNotifications.enabled"
           (click)="toggleNotification('marketingNotifications')">
        <div class="toggle-slider"></div>
      </div>
    </div>

    <div class="notification-New-Partners">
      <div class="notification-info">
        <h4>{{ notificationSettings.newPartners.title }}</h4>
        <p>{{ notificationSettings.newPartners.description }}</p>
      </div>
      <div class="toggle-switch"
           [class.active]="notificationSettings.newPartners.enabled"
           (click)="toggleNotification('newPartners')">
        <div class="toggle-slider"></div>
      </div>
    </div>

    <div class="notification-App-Updates">
      <div class="notification-info">
        <h4>{{ notificationSettings.appUpdates.title }}</h4>
        <p>{{ notificationSettings.appUpdates.description }}</p>
      </div>
      <div class="toggle-switch"
           [class.active]="notificationSettings.appUpdates.enabled"
           (click)="toggleNotification('appUpdates')">
        <div class="toggle-slider"></div>
      </div>
    </div>
  </div>
</div>

<!-- New class for the notification panel shown after clicking the bell icon -->
<div class="notifications-panel" *ngIf="!isUserProfilePage" [class.showHide]="showPanel" [class.dark-mode]="isDarkMode" [style.display]="showPanel ? 'block' : 'none'">
  <div class="notification-header">
    <h3>Notifications</h3>
    <button class="settings-icon" (click)="navigateToSettings()">
      <i class="fas fa-cog"></i>
    </button>
  </div>

  <div class="notification-content" *ngIf="selectedNotification">
    <!-- Show selected notification details -->
    <div class="notification-detail">
      <button class="back-button" (click)="backToNotificationList()">
        <i class="fas fa-arrow-left"></i> Back
      </button>
      <div class="notification-detail-header">
        <h4>{{ selectedNotification.title }}</h4>
        <small>{{ selectedNotification.time | date:'medium' }}</small>
      </div>
      <div class="notification-detail-body">
        <p>{{ selectedNotification.message }}</p>
        <div class="notification-detail-content" *ngIf="selectedNotification.detailContent">
          {{ selectedNotification.detailContent }}
        </div>
      </div>
    </div>
  </div>

  <div class="notification-list" *ngIf="!selectedNotification">
    <!-- List of notifications -->
    <div *ngIf="notifications && notifications.length > 0">
      <div *ngFor="let notification of notifications"
           class="notification-list-item"
           [class.unread]="!notification.read"
           (click)="selectNotification(notification)">
        <div class="notification-list-info">
          <h4>{{ notification.title }}</h4>
          <p>{{ notification.message }}</p>
          <small>{{ notification.time | date:'short' }}</small>
        </div>
      </div>
    </div>
    <div class="empty-notifications" *ngIf="!notifications || notifications.length === 0">
      <p>No new notifications</p>
    </div>
  </div>
</div>
