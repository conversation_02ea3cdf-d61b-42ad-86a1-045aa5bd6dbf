import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule } from '@angular/router';
import { SharedModule } from '../shared/shared.module';
import { AnalyticsComponent } from './analytics.component';

@NgModule({
  imports: [
    CommonModule,
    SharedModule,
    AnalyticsComponent,
    RouterModule.forChild([
      { path: '', component: AnalyticsComponent }
    ])
  ]
})
export class AnalyticsModule { }
