import { Component, OnInit, OnDestroy } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { Router, RouterModule, ActivatedRoute } from '@angular/router';
import { TopNavbarComponent } from '../top-navbar/top-navbar.component';
import { SidebarComponent } from '../sidebar/sidebar.component';
import { Budget, BudgetPeriod } from '../models/budget.model';
import { BudgetService } from '../services/budget.service';
import { ThemeService } from '../services/theme.service';
import { NotificationService } from '../services/notification.service';
import { Subject } from 'rxjs';
import { takeUntil } from 'rxjs/operators';

@Component({
  selector: 'app-new-budget',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    RouterModule,
    TopNavbarComponent,
    SidebarComponent
  ],
  templateUrl: './new-budget.component.html',
  styleUrls: ['./new-budget.component.scss']
})
export class NewBudgetComponent implements OnInit, OnDestroy {
  // Expose BudgetPeriod enum to the template
  BudgetPeriod = BudgetPeriod;

  budget: Budget = {
    id: '',
    name: '',
    period: BudgetPeriod.OneMonth,
    amount: 0,
    category: '',
    createdAt: new Date(),
    notifications: {
      budgetOverrun: true,
      riskOfOverrun: true
    }
  };

  availableCategories: string[] = [];
  showNotificationSettings: boolean = false;
  isDarkMode: boolean = false;
  pendingAmount: number = 0;
  autoAddToNewBudget: boolean = false;
  private destroy$ = new Subject<void>();

  constructor(
    private budgetService: BudgetService,
    private router: Router,
    private route: ActivatedRoute,
    private themeService: ThemeService,
    private notificationService: NotificationService
  ) {}

  ngOnInit(): void {
    // Subscribe to theme changes
    this.themeService.isDarkMode$
      .pipe(takeUntil(this.destroy$))
      .subscribe(isDark => {
        this.isDarkMode = isDark;
      });

    // Get available categories
    this.availableCategories = this.budgetService.getAvailableCategories();

    // Check for pending amount from query parameters
    this.route.queryParams
      .pipe(takeUntil(this.destroy$))
      .subscribe(params => {
        if (params['pendingAmount']) {
          this.pendingAmount = parseFloat(params['pendingAmount']);
          this.autoAddToNewBudget = params['autoAdd'] === 'true';

          // Show a message about the pending amount
          if (this.pendingAmount > 0) {
            // Set a minimum budget amount to accommodate the pending amount
            this.budget.amount = Math.max(this.pendingAmount * 2, 100);
          }
        } else {
          // Check localStorage as a fallback
          const storedAmount = localStorage.getItem('pending-budget-amount');
          if (storedAmount) {
            this.pendingAmount = parseFloat(storedAmount);
            this.autoAddToNewBudget = true;

            // Set a minimum budget amount to accommodate the pending amount
            this.budget.amount = Math.max(this.pendingAmount * 2, 100);

            // Clear the stored amount to prevent it from being used again
            localStorage.removeItem('pending-budget-amount');
          }
        }
      });
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  toggleNotificationSettings(): void {
    this.showNotificationSettings = !this.showNotificationSettings;
  }

  saveBudget(): void {
    if (this.validateBudget()) {
      // Add the budget
      this.budgetService.addBudget(this.budget);

      // If there's a pending amount and autoAddToNewBudget is true, add it to the budget
      if (this.pendingAmount > 0 && this.autoAddToNewBudget) {
        // Add the pending amount to the budget
        const updatedBudget = this.budgetService.addTicketsToBudget(this.budget.id, this.pendingAmount);

        if (updatedBudget) {
          // Show a notification
          this.notificationService.addNotification({
            title: 'Budget Updated',
            message: `Added ${this.pendingAmount} TND to ${updatedBudget.name}`,
            read: false,
            time: new Date()
          });
        }
      }

      // Navigate back to the budget page
      this.router.navigate(['/budget']);
    }
  }

  cancel(): void {
    this.router.navigate(['/budget']);
  }

  private validateBudget(): boolean {
    // Basic validation
    if (!this.budget.name || this.budget.name.trim() === '') {
      alert('Please enter a budget name');
      return false;
    }

    if (!this.budget.amount || this.budget.amount <= 0) {
      alert('Please enter a valid budget amount');
      return false;
    }

    if (!this.budget.category || this.budget.category.trim() === '') {
      alert('Please select a category');
      return false;
    }

    return true;
  }
}
