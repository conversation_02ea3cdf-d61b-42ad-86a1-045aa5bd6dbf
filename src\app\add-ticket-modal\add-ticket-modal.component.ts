import { Component, EventEmitter, Output, OnInit, AfterViewInit, ElementRef, ViewChild, HostListener } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';

@Component({
  selector: 'app-add-ticket-modal',
  standalone: true,
  imports: [CommonModule, FormsModule],
  templateUrl: './add-ticket-modal.component.html',
  styleUrls: ['./add-ticket-modal.component.scss']
})
export class AddTicketModalComponent implements OnInit, AfterViewInit {
  @Output() close = new EventEmitter<void>();
  @Output() save = new EventEmitter<any>();

  @ViewChild('modalContent') modalContent!: ElementRef;

  // Input mode selection
  inputMode: 'qrcode' | 'manual' = 'manual';

  ticketId: string = '#';
  brandName: string = '';
  totalAmount: number = 0;
  products: Array<{name: string, price: number}> = [];
  isCollaboratingBrand: boolean = false;
  editingProductIndex: number = -1;
  tempProduct: {name: string, price: number} = {name: '', price: 0};
  newProductName: string = '';
  newProductPrice: number = 0;

  // QR Code related properties
  qrCodePreview: string | null = null;
  qrCodeFile: File | null = null;

  // Collaborating brands array
  collaboratingBrands = [
    'Carrefour',
    'Zen',
    'Monoprix',
    'Aziza',
    'STRASS SOUSSE MALL'
  ];
  filteredBrands: string[] = [];
  showBrandSuggestions: boolean = false;

  // Initialize filtered brands
  ngOnInit() {
    this.filteredBrands = [...this.collaboratingBrands];
  }

  ngAfterViewInit() {
    // Check if scrollbar is needed after view is initialized
    setTimeout(() => this.checkScrollNeeded(), 100);
  }

  @HostListener('window:resize')
  onResize() {
    // Check if scrollbar is needed when window is resized
    this.checkScrollNeeded();
  }

  // Check if content requires scrollbar
  checkScrollNeeded() {
    if (this.modalContent && this.modalContent.nativeElement) {
      const element = this.modalContent.nativeElement;
      const hasScrollbar = element.scrollHeight > element.clientHeight;

      if (hasScrollbar) {
        element.classList.remove('no-scroll');
      } else {
        element.classList.add('no-scroll');
      }
    }
  }

  // Handle brand name input
  onBrandNameInput() {
    // Filter brands based on input
    const input = this.brandName.toLowerCase();
    this.filteredBrands = this.collaboratingBrands.filter(brand =>
      brand.toLowerCase().includes(input)
    );

    // Check if the brand is a collaborating brand
    this.checkBrandCollaboration();
  }

  // Handle brand selection
  selectBrand(brand: string) {
    this.brandName = brand;
    this.showBrandSuggestions = false;
    this.checkBrandCollaboration();
  }

  // Handle input blur
  onBrandInputBlur() {
    // Delay hiding suggestions to allow click events to complete
    setTimeout(() => {
      this.showBrandSuggestions = false;
    }, 200);
  }

  checkBrandCollaboration() {
    this.isCollaboratingBrand = this.collaboratingBrands.some(brand =>
      brand.toLowerCase() === this.brandName.toLowerCase()
    );

    if (!this.isCollaboratingBrand) {
      this.products = [];
      this.totalAmount = 0;
    }
  }

  addProduct() {
    if (this.newProductName && this.newProductPrice > 0) {
      this.products.push({
        name: this.newProductName,
        price: this.newProductPrice
      });
      this.newProductName = '';
      this.newProductPrice = 0;
      this.calculateTotal();
      // Check if scrollbar is needed after adding product
      setTimeout(() => this.checkScrollNeeded(), 100);
    }
  }

  removeProduct(index: number) {
    this.products.splice(index, 1);
    this.calculateTotal();
    // Check if scrollbar is needed after removing product
    setTimeout(() => this.checkScrollNeeded(), 100);
  }

  calculateTotal() {
    if (this.isCollaboratingBrand) {
      this.totalAmount = this.products.reduce((sum, product) => sum + product.price, 0);
    }
  }

  editProduct(index: number) {
    this.editingProductIndex = index;
    this.tempProduct = { ...this.products[index] };
  }

  saveProductEdit() {
    if (this.editingProductIndex !== -1) {
      this.products[this.editingProductIndex] = { ...this.tempProduct };
      this.calculateTotal();
      this.editingProductIndex = -1;
      this.tempProduct = { name: '', price: 0 };
    }
  }

  cancelEdit() {
    this.editingProductIndex = -1;
    this.tempProduct = { name: '', price: 0 };
  }

  // QR Code upload handling
  onQrCodeUpload(event: Event) {
    const input = event.target as HTMLInputElement;
    if (input.files && input.files.length > 0) {
      this.qrCodeFile = input.files[0];

      // Create a preview of the uploaded QR code
      const reader = new FileReader();
      reader.onload = () => {
        this.qrCodePreview = reader.result as string;
        // Check if scrollbar is needed after content change
        setTimeout(() => this.checkScrollNeeded(), 100);
      };
      reader.readAsDataURL(this.qrCodeFile);
    }
  }

  // Remove QR code
  removeQrCode() {
    this.qrCodePreview = null;
    this.qrCodeFile = null;
    // Check if scrollbar is needed after content change
    setTimeout(() => this.checkScrollNeeded(), 100);
  }

  saveTicket() {
    let ticketData;

    if (this.inputMode === 'qrcode') {
      // For QR code mode, we just save the QR code image
      // In a real app, you would process the QR code to extract ticket information
      ticketData = {
        inputMode: 'qrcode',
        qrCodeImage: this.qrCodePreview,
        date: new Date().toISOString().split('T')[0]
      };
    } else {
      // For manual input mode, save all the form data
      ticketData = {
        inputMode: 'manual',
        ticketId: this.ticketId.startsWith('#') ? this.ticketId : '#' + this.ticketId,
        brandName: this.brandName,
        totalAmount: this.totalAmount,
        products: this.isCollaboratingBrand ? this.products : [],
        isCollaboratingBrand: this.isCollaboratingBrand,
        date: new Date().toISOString().split('T')[0]
      };
    }

    this.save.emit(ticketData);
    this.close.emit();
  }
}