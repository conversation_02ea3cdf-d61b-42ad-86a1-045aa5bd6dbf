import { Component, HostListener } from '@angular/core';
import { CommonModule } from '@angular/common';
import { SidebarService } from '../sidebar/sidebar.service';

@Component({
  selector: 'app-analytics',
  standalone: true,
  imports: [CommonModule],
  templateUrl: './analytics.component.html',
  styleUrls: ['./analytics.component.scss']
})
export class AnalyticsComponent {
  tooltip: string | null = null;
  isMobile: boolean = window.innerWidth <= 768;

  constructor(private sidebarService: SidebarService) {}

  @HostListener('window:resize', ['$event'])
  onResize() {
    this.isMobile = window.innerWidth <= 768;
  }

  showTooltip(type: string) {
    this.tooltip = type;
  }

  hideTooltip() {
    this.tooltip = null;
  }

  highlightItem(event: Event) {
    const target = event.currentTarget as HTMLElement;
    target.style.backgroundColor = '#F5F5F5';
  }

  unhighlightItem(event: Event) {
    const target = event.currentTarget as HTMLElement;
    target.style.backgroundColor = 'transparent';
  }

  toggleSidebar() {
    this.sidebarService.toggleSidebar();
  }
}