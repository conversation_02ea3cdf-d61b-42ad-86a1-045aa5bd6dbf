.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
  animation: fadeIn 0.3s ease;
}

.modal-content {
  background: var(--card-bg, white);
  border-radius: 12px;
  width: 90%;
  max-width: 500px;
  max-height: 90vh;
  overflow-y: auto;
  overflow-x: hidden;
  position: relative;
  border: 1px solid rgba(171, 85, 247, 0.2);
  box-shadow:
    0 10px 30px rgba(107, 72, 255, 0.15),
    0 6px 15px rgba(168, 85, 247, 0.1),
    0 0 0 1px rgba(171, 85, 247, 0.05),
    inset 0 0 0 1px rgba(255, 255, 255, 0.3);
  animation: modalGlow 1.5s ease-in-out infinite alternate;
  animation: slideUp 0.3s ease;

  /* Custom scrollbar styling */
  scrollbar-width: thin;
  scrollbar-color: rgba(107, 72, 255, 0.3) transparent;

  &::-webkit-scrollbar {
    width: 6px;
    height: 6px;
  }

  &::-webkit-scrollbar-track {
    background: transparent;
    margin: 10px 0;
  }

  &::-webkit-scrollbar-thumb {
    background-color: rgba(107, 72, 255, 0.3);
    border-radius: 10px;
    border: 2px solid transparent;
  }

  &::-webkit-scrollbar-thumb:hover {
    background-color: rgba(107, 72, 255, 0.5);
  }

  &::before {
    content: '';
    position: absolute;
    top: -2px;
    left: -2px;
    right: -2px;
    bottom: -2px;
    background: linear-gradient(135deg, rgba(107, 72, 255, 0.4), rgba(168, 85, 247, 0.4), rgba(183, 148, 244, 0.4));
    border-radius: 14px;
    z-index: -1;
    opacity: 0.5;
    filter: blur(8px);
  }
}

@keyframes modalGlow {
  from {
    box-shadow:
      0 10px 30px rgba(107, 72, 255, 0.15),
      0 6px 15px rgba(168, 85, 247, 0.1),
      0 0 0 1px rgba(171, 85, 247, 0.05),
      inset 0 0 0 1px rgba(255, 255, 255, 0.3);
  }
  to {
    box-shadow:
      0 15px 40px rgba(107, 72, 255, 0.2),
      0 8px 20px rgba(168, 85, 247, 0.15),
      0 0 0 1px rgba(171, 85, 247, 0.1),
      inset 0 0 0 1px rgba(255, 255, 255, 0.4);
  }
}

.modal-header {
  padding: 20px;
  border-bottom: 1px solid rgba(171, 85, 247, 0.15);
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: linear-gradient(to bottom, rgba(107, 72, 255, 0.03), var(--card-bg, rgba(255, 255, 255, 0)));

  h2 {
    margin: 0;
    font-size: 1.5rem;
    color: var(--text-color, #333);
    font-weight: 600;
    position: relative;

    &::after {
      content: '';
      position: absolute;
      bottom: -6px;
      left: 0;
      width: 40px;
      height: 3px;
      background: linear-gradient(90deg, #6B48FF, #A855F7);
      border-radius: 2px;
    }
  }

  .close-btn {
    background: none;
    border: none;
    font-size: 1.2rem;
    cursor: pointer;
    color: var(--text-color, #666);
    padding: 8px;
    border-radius: 50%;
    transition: all 0.3s ease;
    width: 36px;
    height: 36px;
    display: flex;
    align-items: center;
    justify-content: center;

    &:hover {
      color: #ff4757;
      background-color: rgba(255, 71, 87, 0.1);
      transform: rotate(90deg);
    }
  }
}

.modal-body {
  padding: 24px;
  position: relative;

  .product-info {
    display: flex;
    gap: 20px;
    margin-bottom: 30px;
    background: rgba(107, 72, 255, 0.03);
    border-radius: 12px;
    padding: 16px;
    border: 1px solid rgba(171, 85, 247, 0.1);

    .product-image-container {
      width: 120px;
      height: 120px;
      border-radius: 10px;
      overflow: hidden;
      flex-shrink: 0;
      box-shadow:
        0 8px 20px rgba(107, 72, 255, 0.15),
        0 4px 10px rgba(168, 85, 247, 0.1);
      border: 2px solid rgba(255, 255, 255, 0.8);
      position: relative;

      &::after {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        border-radius: 8px;
        box-shadow: inset 0 0 0 1px rgba(171, 85, 247, 0.2);
        pointer-events: none;
      }

      .product-image {
        width: 100%;
        height: 100%;
        object-fit: cover;
        transition: transform 0.5s ease;

        &:hover {
          transform: scale(1.05);
        }
      }
    }

    .product-details {
      flex: 1;
      display: flex;
      flex-direction: column;
      justify-content: center;

      h3 {
        margin: 0 0 12px;
        font-size: 20px;
        color: var(--text-color, #333);
        font-weight: 600;
      }

      .brand-container, .price-container {
        display: flex;
        align-items: center;
        gap: 8px;
        margin-bottom: 8px;

        i {
          color: #A855F7;
          font-size: 14px;
          width: 20px;
          height: 20px;
          display: flex;
          align-items: center;
          justify-content: center;
          background: rgba(168, 85, 247, 0.1);
          border-radius: 50%;
        }

        p {
          margin: 0;
          font-size: 15px;
          color: var(--text-color, #666);
        }
      }

      .price-container {
        .price {
          font-weight: 700;
          font-size: 18px;
          color: var(--text-color, #333);

          .currency {
            font-size: 14px;
            opacity: 0.8;
            font-weight: 500;
          }
        }
      }
    }
  }

  .quantity-section {
    margin-bottom: 30px;
    padding: 20px;
    border-radius: 12px;
    background: var(--card-bg, white);
    border: 1px solid rgba(171, 85, 247, 0.1);
    box-shadow: 0 4px 15px rgba(107, 72, 255, 0.05);

    .section-title {
      display: flex;
      align-items: center;
      gap: 10px;
      margin-bottom: 20px;

      i {
        color: #6B48FF;
        font-size: 16px;
        width: 32px;
        height: 32px;
        display: flex;
        align-items: center;
        justify-content: center;
        background: rgba(107, 72, 255, 0.1);
        border-radius: 50%;
      }

      h4 {
        margin: 0;
        font-size: 18px;
        color: var(--text-color, #333);
        font-weight: 600;
      }
    }

    .quantity-selector {
      .quantity-controls {
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 15px;

        input {
          width: 80px;
          text-align: center;
          padding: 12px;
          border: 1px solid rgba(171, 85, 247, 0.2);
          border-radius: 8px;
          font-size: 18px;
          font-weight: 600;
          color: var(--text-color, #333);
          background: var(--input-bg, white);
          box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.05);
          -moz-appearance: textfield; /* Firefox */
          transition: all 0.3s ease;

          &:focus {
            outline: none;
            border-color: #6B48FF;
            box-shadow:
              0 0 0 3px rgba(107, 72, 255, 0.1),
              inset 0 2px 4px rgba(0, 0, 0, 0.05);
          }

          &::-webkit-outer-spin-button,
          &::-webkit-inner-spin-button {
            -webkit-appearance: none;
            margin: 0;
          }
        }

        .quantity-btn {
          width: 44px;
          height: 44px;
          border-radius: 50%;
          border: 1px solid rgba(171, 85, 247, 0.2);
          background: var(--card-bg, white);
          color: var(--text-color, #333);
          font-size: 16px;
          display: flex;
          align-items: center;
          justify-content: center;
          cursor: pointer;
          transition: all 0.3s ease;
          box-shadow:
            0 4px 8px rgba(107, 72, 255, 0.1),
            0 1px 3px rgba(0, 0, 0, 0.05);

          &:hover {
            transform: translateY(-2px);
            box-shadow:
              0 6px 12px rgba(107, 72, 255, 0.15),
              0 2px 4px rgba(0, 0, 0, 0.1);
          }

          &:active {
            transform: translateY(0);
            box-shadow:
              0 2px 4px rgba(107, 72, 255, 0.1),
              0 1px 2px rgba(0, 0, 0, 0.05);
          }

          &.decrement {
            color: #ff4757;

            &:hover {
              background: rgba(255, 71, 87, 0.1);
            }
          }

          &.increment {
            color: #2ecc71;

            &:hover {
              background: rgba(46, 204, 113, 0.1);
            }
          }

          i {
            font-size: 14px;
          }
        }
      }
    }
  }

  .total-section {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px;
    border-radius: 12px;
    background: linear-gradient(135deg, rgba(107, 72, 255, 0.05), rgba(168, 85, 247, 0.05));
    border: 1px solid rgba(171, 85, 247, 0.15);
    margin-bottom: 10px;

    .total-label {
      display: flex;
      align-items: center;
      gap: 12px;

      i {
        color: #6B48FF;
        font-size: 18px;
        width: 36px;
        height: 36px;
        display: flex;
        align-items: center;
        justify-content: center;
        background: rgba(107, 72, 255, 0.1);
        border-radius: 50%;
      }

      h3 {
        margin: 0;
        font-size: 18px;
        color: var(--text-color, #333);
        font-weight: 600;
      }
    }

    .total-price {
      font-weight: 700;
      font-size: 24px;
      color: #6B48FF;
      margin: 0;
      text-shadow: 0 1px 1px rgba(0, 0, 0, 0.1);

      .currency {
        font-size: 16px;
        opacity: 0.8;
        font-weight: 600;
      }
    }
  }
}

.modal-footer {
  padding: 24px;
  border-top: 1px solid rgba(171, 85, 247, 0.15);
  display: flex;
  justify-content: flex-end;
  gap: 15px;
  background: linear-gradient(to top, rgba(107, 72, 255, 0.03), var(--card-bg, rgba(255, 255, 255, 0)));
  border-radius: 0 0 12px 12px;

  button {
    padding: 12px 24px;
    border-radius: 8px;
    font-size: 15px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 8px;

    i {
      font-size: 14px;
    }
  }

  .cancel-btn {
    background: var(--card-bg, rgba(255, 255, 255, 0.8));
    border: 1px solid rgba(171, 85, 247, 0.2);
    color: var(--text-color, #666);
    box-shadow:
      0 2px 6px rgba(0, 0, 0, 0.05),
      inset 0 1px 0 rgba(255, 255, 255, 0.3);
    position: relative;
    overflow: hidden;

    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: linear-gradient(to bottom, rgba(255, 255, 255, 0.2), rgba(255, 255, 255, 0));
      opacity: 0;
      transition: opacity 0.3s ease;
    }

    &:hover {
      background: var(--hover-bg, rgba(250, 250, 250, 0.95));
      color: #A855F7;
      transform: translateY(-2px);
      border-color: rgba(171, 85, 247, 0.4);
      box-shadow:
        0 4px 10px rgba(0, 0, 0, 0.08),
        inset 0 1px 0 rgba(255, 255, 255, 0.5);

      &::before {
        opacity: 1;
      }

      i {
        transform: rotate(90deg);
      }
    }

    &:active {
      transform: translateY(1px);
      box-shadow:
        0 1px 3px rgba(0, 0, 0, 0.05),
        inset 0 1px 0 rgba(255, 255, 255, 0.8);
    }
  }

  .confirm-btn {
    background: linear-gradient(135deg, #6B48FF, #A855F7);
    color: white;
    border: none;
    box-shadow:
      0 4px 15px rgba(107, 72, 255, 0.25),
      0 2px 4px rgba(168, 85, 247, 0.2),
      inset 0 1px 1px rgba(255, 255, 255, 0.3);
    text-shadow: 0 1px 1px rgba(0, 0, 0, 0.2);
    position: relative;
    overflow: hidden;

    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: linear-gradient(to bottom, rgba(255, 255, 255, 0.2), rgba(255, 255, 255, 0));
      opacity: 0;
      transition: opacity 0.3s ease;
    }

    &:hover {
      transform: translateY(-2px);
      box-shadow:
        0 8px 20px rgba(107, 72, 255, 0.3),
        0 4px 8px rgba(168, 85, 247, 0.25),
        inset 0 1px 1px rgba(255, 255, 255, 0.4);

      &::before {
        opacity: 1;
      }

      i {
        transform: scale(1.2);
      }
    }

    &:active {
      transform: translateY(1px);
      box-shadow:
        0 2px 8px rgba(107, 72, 255, 0.2),
        0 1px 3px rgba(168, 85, 247, 0.15),
        inset 0 1px 1px rgba(255, 255, 255, 0.2);
    }
  }
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes slideUp {
  from { transform: translateY(20px); opacity: 0; }
  to { transform: translateY(0); opacity: 1; }
}

// Mobile styles
@media (max-width: 768px) {
  .modal-content {
    width: 95%;
    max-width: 350px;
  }

  .modal-header h2 {
    font-size: 18px;
  }

  .modal-body {
    padding: 15px;

    .product-info h3 {
      font-size: 16px;
    }

    .quantity-controls {
      .quantity-btn {
        width: 32px;
        height: 32px;
      }

      input {
        width: 50px;
        padding: 6px;
      }
    }

    .total-section {
      h3 {
        font-size: 16px;
      }

      .total-price {
        font-size: 18px;
      }
    }
  }

  .modal-footer {
    padding: 12px;

    button {
      padding: 8px 12px;
      font-size: 13px;
    }
  }
}

// Dark mode styles
:host-context(.dark-mode) {
  .modal-content {
    background-color: #1e1e2d;
    border-color: rgba(171, 85, 247, 0.3);
    box-shadow:
      0 10px 30px rgba(0, 0, 0, 0.3),
      0 6px 15px rgba(107, 72, 255, 0.15),
      0 0 0 1px rgba(171, 85, 247, 0.1),
      inset 0 0 0 1px rgba(255, 255, 255, 0.05);

    &::before {
      opacity: 0.3;
      filter: blur(12px);
    }
  }

  .modal-header {
    border-bottom-color: rgba(171, 85, 247, 0.2);
    background: linear-gradient(to bottom, rgba(107, 72, 255, 0.05), rgba(0, 0, 0, 0));

    h2 {
      color: white;
    }

    .close-btn {
      color: #aaa;

      &:hover {
        background-color: rgba(255, 71, 87, 0.15);
        color: #ff6b81;
      }
    }
  }

  .modal-body {
    .product-info {
      background: rgba(107, 72, 255, 0.05);
      border-color: rgba(171, 85, 247, 0.2);

      .product-image-container {
        border-color: rgba(255, 255, 255, 0.1);
        box-shadow:
          0 8px 20px rgba(0, 0, 0, 0.3),
          0 4px 10px rgba(107, 72, 255, 0.2);

        &::after {
          box-shadow: inset 0 0 0 1px rgba(171, 85, 247, 0.3);
        }
      }

      .product-details {
        h3 {
          color: white;
        }

        .brand-container, .price-container {
          i {
            background: rgba(168, 85, 247, 0.2);
          }

          p {
            color: #aaa;
          }
        }

        .price-container .price {
          color: white;
        }
      }
    }

    .quantity-section {
      background: #252536;
      border-color: rgba(171, 85, 247, 0.2);
      box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);

      .section-title {
        i {
          background: rgba(107, 72, 255, 0.2);
        }

        h4 {
          color: white;
        }
      }

      .quantity-selector .quantity-controls {
        input {
          background-color: #1e1e2d;
          border-color: rgba(171, 85, 247, 0.3);
          color: white;
          box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.2);

          &:focus {
            border-color: #A855F7;
            box-shadow:
              0 0 0 3px rgba(168, 85, 247, 0.2),
              inset 0 2px 4px rgba(0, 0, 0, 0.2);
          }
        }

        .quantity-btn {
          background-color: #252536;
          border-color: rgba(171, 85, 247, 0.3);
          box-shadow:
            0 4px 8px rgba(0, 0, 0, 0.2),
            0 1px 3px rgba(0, 0, 0, 0.1);

          &:hover {
            box-shadow:
              0 6px 12px rgba(0, 0, 0, 0.25),
              0 2px 4px rgba(107, 72, 255, 0.2);
          }

          &.decrement:hover {
            background: rgba(255, 71, 87, 0.15);
          }

          &.increment:hover {
            background: rgba(46, 204, 113, 0.15);
          }
        }
      }
    }

    .total-section {
      background: linear-gradient(135deg, rgba(107, 72, 255, 0.1), rgba(168, 85, 247, 0.1));
      border-color: rgba(171, 85, 247, 0.25);

      .total-label {
        i {
          background: rgba(107, 72, 255, 0.2);
        }

        h3 {
          color: white;
        }
      }

      .total-price {
        color: #A855F7;
        text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
      }
    }
  }

  .modal-footer {
    border-top-color: rgba(171, 85, 247, 0.2);
    background: linear-gradient(to top, rgba(107, 72, 255, 0.05), rgba(0, 0, 0, 0));

    .cancel-btn {
      background: rgba(255, 255, 255, 0.05);
      border-color: rgba(171, 85, 247, 0.3);
      color: #aaa;
      box-shadow:
        0 2px 6px rgba(0, 0, 0, 0.1),
        inset 0 1px 0 rgba(255, 255, 255, 0.05);

      &:hover {
        background: rgba(255, 255, 255, 0.1);
        color: #A855F7;
        border-color: rgba(171, 85, 247, 0.5);
        box-shadow:
          0 4px 10px rgba(0, 0, 0, 0.15),
          inset 0 1px 0 rgba(255, 255, 255, 0.1);
      }
    }

    .confirm-btn {
      background: linear-gradient(135deg, #6B48FF, #A855F7);
      box-shadow:
        0 4px 15px rgba(0, 0, 0, 0.3),
        0 2px 4px rgba(107, 72, 255, 0.3),
        inset 0 1px 1px rgba(255, 255, 255, 0.1);

      &:hover {
        box-shadow:
          0 8px 20px rgba(0, 0, 0, 0.4),
          0 4px 8px rgba(107, 72, 255, 0.35),
          inset 0 1px 1px rgba(255, 255, 255, 0.2);
      }
    }
  }
}
