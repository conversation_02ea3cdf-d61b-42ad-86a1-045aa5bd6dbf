.budget-container {
  padding: 20px;
  max-width: 600px;
  margin: 0 auto;
  font-family: 'Inter', sans-serif;

  // Dark mode styles
  &.dark-mode {
    color: #fff;

    .budget-header h1 {
      color: #fff;
    }

    .budget-overview,
    .budget-categories,
    .budget-list,
    .empty-budget-message {
      background-color: #2d2d2d;
      box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);

      h2, h3 {
        color: #fff;
      }

      p {
        color: #aaa;
      }

      .no-budget-selected {
        color: #aaa;
      }
    }

    .budget-item {
      background-color: #333;
      border-color: #444;

      .budget-item-header {
        h3 {
          color: #fff;
        }

        .budget-item-period {
          background-color: #444;
          color: #aaa;
        }
      }

      .budget-item-details {
        .budget-item-amount {
          color: #fff;
        }

        .budget-item-category {
          color: #aaa;
        }
      }

      .budget-item-stats {
        color: #aaa;
      }
    }

    .budget-label {
      color: #aaa;
    }

    .progress-bar {
      background-color: #333;
    }

    .green-progress {
      background-color: #2ecc71 !important;
      box-shadow: 0 0 5px rgba(46, 204, 113, 0.5);
    }

    .red-progress {
      background-color: #e74c3c !important;
      box-shadow: 0 0 5px rgba(231, 76, 60, 0.5);
    }

    .total-progress-fill {
      background-color: #2ecc71;
      box-shadow: 0 0 5px rgba(46, 204, 113, 0.5);

      &::after {
        background: repeating-linear-gradient(
          45deg,
          rgba(255, 255, 255, 0.05),
          rgba(255, 255, 255, 0.05) 10px,
          rgba(255, 255, 255, 0.1) 10px,
          rgba(255, 255, 255, 0.1) 20px
        );
      }
    }

    .total-budget-item {
      border-left-color: #2ecc71;
      border-right-color: #2ecc71;
    }

    .budget-value {
      color: #fff;
    }

    .progress-bar {
      background-color: #444;
    }

    .category-item {
      background-color: #333;

      &:hover {
        background-color: #3a3a3a;
      }
    }

    .category-name {
      color: #fff;
    }

    .category-percentage {
      color: #aaa;
    }

    .budget-item {
      background-color: #333;
      border: 1px solid #444;

      &:hover {
        background-color: #3a3a3a;
      }

      h3 {
        color: #fff;
      }

      .budget-item-period,
      .budget-item-category {
        color: #aaa;
        background-color: #444;

        .edit-icon {
          color: #a29bfe;

          &:hover {
            color: #6c5ce7;
          }
        }
      }

      .budget-item-amount {
        color: #fff;
      }

      .budget-item-stats {
        color: #aaa;
      }
    }
  }
}

.budget-header {
  margin-bottom: 20px;
  text-align: center;

  h1 {
    font-size: 24px;
    font-weight: 600;
    margin: 0;
  }
}

// Budget List Styles
.budget-list {
  background-color: #fff;
  border-radius: 12px;
  padding: 20px;
  margin-bottom: 20px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);

  h2 {
    font-size: 18px;
    font-weight: 600;
    margin: 0 0 20px 0;
  }
}

.budget-items {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.budget-item {
  background-color: #f9f9f9;
  border-radius: 8px;
  padding: 15px;
  border: 1px solid #eee;
  transition: transform 0.2s, box-shadow 0.2s;

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
  }
}

.budget-item-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;

  h3 {
    font-size: 16px;
    font-weight: 600;
    margin: 0;
  }

  .budget-item-period {
    font-size: 12px;
    color: #666;
    background-color: #f0f0f0;
    padding: 3px 8px;
    border-radius: 12px;
    display: flex;
    align-items: center;

    .edit-icon {
      margin-right: 8px;
      cursor: pointer;
      color: #6c5ce7;
      transition: transform 0.2s;

      &:hover {
        transform: scale(1.2);
      }
    }
  }
}

.budget-item-details {
  display: flex;
  justify-content: space-between;
  margin-bottom: 10px;

  .budget-item-amount {
    font-weight: 600;
    font-size: 18px;
  }

  .budget-item-category {
    color: #666;
    font-size: 14px;
  }
}

.budget-item-progress {
  .progress-bar {
    margin-bottom: 5px;
  }

  .budget-item-stats {
    display: flex;
    justify-content: space-between;
    font-size: 12px;
    color: #666;
  }
}

.budget-overview {
  background-color: #fff;
  border-radius: 12px;
  padding: 20px;
  margin-bottom: 20px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);

  h2 {
    font-size: 18px;
    font-weight: 600;
    margin: 0 0 20px 0;
  }
}

.budget-progress {
  display: flex;
  justify-content: space-between;
  gap: 30px;
  flex-wrap: nowrap;
  align-items: center;
}

.budget-progress-item {
  flex: 1;
  min-width: 200px;
  margin-bottom: 15px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  padding: 15px;
  border-radius: 8px;
  transition: transform 0.2s;

  &:hover {
    transform: translateY(-2px);
  }
}

.total-budget-item {
  border-left: 3px solid #4cd137;
  border-right: 3px solid #4cd137;
  padding: 0 20px;
  margin: 0 10px;

  .budget-label {
    font-weight: 500;
    color: #4cd137;
  }

  .budget-value {
    font-size: 20px;
    font-weight: 700;
    color: #4cd137;
  }
}

.budget-label {
  font-size: 14px;
  color: #666;
  margin-bottom: 5px;
}

.budget-value {
  font-size: 18px;
  font-weight: 600;
  margin-bottom: 10px;
}

.budget-percentage {
  font-size: 14px;
  color: #4cd137;
}

.progress-bar {
  height: 10px;
  background-color: #f1f1f1;
  border-radius: 5px;
  overflow: hidden;
  box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.1);
  margin-top: 10px;
}

.progress-fill {
  height: 100%;
  border-radius: 5px;
  background-color: #4cd137; // Green color
  transition: width 0.3s ease;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.total-progress-fill {
  height: 100%;
  border-radius: 5px;
  background-color: #4cd137; // Green for total budget
  position: relative;
  overflow: hidden;
  transition: width 0.3s ease;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);

  &::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: repeating-linear-gradient(
      45deg,
      rgba(255, 255, 255, 0.1),
      rgba(255, 255, 255, 0.1) 10px,
      rgba(255, 255, 255, 0.2) 10px,
      rgba(255, 255, 255, 0.2) 20px
    );
    animation: shine 1.5s infinite linear;
  }
}

@keyframes shine {
  0% {
    background-position: 0 0;
  }
  100% {
    background-position: 40px 0;
  }
}

.budget-categories {
  background-color: #fff;
  border-radius: 12px;
  padding: 20px;
  margin-bottom: 20px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);

  h2 {
    font-size: 18px;
    font-weight: 600;
    margin: 0 0 20px 0;
  }

  .no-budget-selected {
    text-align: center;
    padding: 20px;
    color: #666;
    font-style: italic;
  }
}

.category-list {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.category-item {
  background-color: #f9f9f9;
  border-radius: 8px;
  padding: 15px;
  cursor: pointer;
  transition: transform 0.2s;

  &:hover {
    transform: scale(1.02);
  }
}

.category-header {
  display: flex;
  justify-content: space-between;
  margin-bottom: 5px;
}

.category-name {
  font-weight: 500;
}

.category-amount {
  font-weight: 600;
  color: #6c5ce7;
}

.chevron-right {
  margin-left: 5px;
  font-size: 18px;
}

.category-percentage {
  font-size: 12px;
  color: #666;
  margin-bottom: 8px;
}

.green-progress {
  background-color: #4cd137 !important; // Green color for good progress
  box-shadow: 0 0 5px rgba(76, 209, 55, 0.5);
}

.red-progress {
  background-color: #e74c3c !important; // Red color for warning
  box-shadow: 0 0 5px rgba(231, 76, 60, 0.5);
}

.empty-budget-message {
  background-color: #fff;
  border-radius: 12px;
  padding: 40px 20px;
  margin-bottom: 20px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
  text-align: center;

  .message-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 15px;

    i {
      font-size: 48px;
      color: #6c5ce7;
      margin-bottom: 10px;
    }

    h3 {
      font-size: 20px;
      font-weight: 600;
      margin: 0;
      color: #333;
    }

    p {
      font-size: 16px;
      color: #666;
      margin: 0;
    }
  }
}

.create-budget-container {
  display: flex;
  justify-content: center;
  margin-top: 30px;
}

.create-budget-btn {
  background-color: #6c5ce7;
  color: white;
  border: none;
  border-radius: 25px;
  padding: 15px 30px;
  font-size: 16px;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s;

  &:hover {
    background-color: #5b4bc4;
  }
}

@media (max-width: 768px) {
  .budget-progress {
    flex-direction: row; /* Keep items on same line */
    gap: 10px;
    flex-wrap: nowrap;
    overflow-x: auto; /* Allow horizontal scrolling if needed */
    padding-bottom: 10px; /* Space for scrollbar */
  }

  .budget-progress-item {
    min-width: 160px; /* Slightly smaller on mobile */
    flex: 0 0 auto; /* Don't grow or shrink */
  }

  .total-budget-item {
    border-left: 3px solid #4cd137;
    border-right: 3px solid #4cd137;
    padding: 0 10px;
    margin: 0 5px;
  }

  .dark-mode .total-budget-item {
    border-left-color: #2ecc71;
    border-right-color: #2ecc71;
  }
}
