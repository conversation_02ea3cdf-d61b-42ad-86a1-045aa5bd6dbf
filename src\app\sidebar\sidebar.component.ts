import { Component, Renderer2, <PERSON>ement<PERSON><PERSON>, <PERSON><PERSON>hild, HostListener, inject } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule, Router } from '@angular/router';
import { SidebarService } from './sidebar.service';
import { UserService } from '../services/user.service';
import { User } from '../models/user.model';
import { AuthService } from '../core/auth/auth.service';

@Component({
  selector: 'app-sidebar',
  templateUrl: './sidebar.component.html',
  styleUrls: ['./sidebar.component.css'],
  standalone: true,
  imports: [CommonModule, RouterModule]
})
export class SidebarComponent {
  @ViewChild('sidebar', { static: false }) sidebar?: ElementRef;
  @ViewChild('sidebarOverlay', { static: false }) sidebarOverlay?: ElementRef;
  @ViewChild('dropdownWindow', { static: false }) dropdownWindow?: ElementRef;
  @ViewChild('userInfoDropdown', { static: false }) userInfoDropdown?: ElementRef;

  isDropdownOpen: boolean = false;
  isUserInfoOpen: boolean = false;
  isPremium: boolean = false;
  userRole: string = '';
  currentUser: User | null = null;

  // Inject AuthService
  private authService = inject(AuthService);

  // Access auth signals for user info
  protected readonly userFullName = this.authService.userFullName;
  protected readonly userEmail = this.authService.userEmail;
  protected readonly userAvatar = this.authService.userAvatar;
  protected readonly authUser = this.authService.currentUser;

  // Helper methods for avatar properties
  get hasCustomAvatar(): boolean {
    const user = this.authUser() as any;
    return user?.hasCustomAvatar || false;
  }

  get avatarPositionY(): number {
    const user = this.authUser() as any;
    return user?.avatarPositionY || 0;
  }

  constructor(
    private renderer: Renderer2,
    private sidebarService: SidebarService,
    private userService: UserService,
    private router: Router
  ) {
    this.userService.userRole$.subscribe(role => {
      this.userRole = role;
    });

    this.userService.currentUser$.subscribe(user => {
      this.currentUser = user;
    });
  }

  ngAfterViewInit() {
    this.sidebarService.sidebarOpen$.subscribe(isOpen => {
      if (this.sidebar && this.sidebarOverlay) {
        if (isOpen) {
          this.renderer.addClass(this.sidebar.nativeElement, 'open');
          this.renderer.addClass(this.sidebarOverlay.nativeElement, 'open');
        } else {
          this.renderer.removeClass(this.sidebar.nativeElement, 'open');
          this.renderer.removeClass(this.sidebarOverlay.nativeElement, 'open');
          this.isDropdownOpen = false;
        }
      }
    });

    if (this.sidebarOverlay) {
      this.renderer.listen(this.sidebarOverlay.nativeElement, 'click', () => {
        this.sidebarService.closeSidebar();
        this.isDropdownOpen = false;
      });
    }
  }

  toggleSidebar() {
    this.sidebarService.toggleSidebar();
  }

  toggleDropdown() {
    this.isDropdownOpen = !this.isDropdownOpen;
  }

  toggleUserInfo() {
    this.isUserInfoOpen = !this.isUserInfoOpen;
  }

  @HostListener('document:click', ['$event'])
  onDocumentClick(event: MouseEvent) {
    const target = event.target as HTMLElement;

    // Handle plan dropdown
    if (this.isDropdownOpen && this.dropdownWindow) {
      if (!this.dropdownWindow.nativeElement.contains(target) && !target.closest('.arrow-icon')) {
        this.isDropdownOpen = false;
      }
    }

    // Handle user info dropdown
    if (this.isUserInfoOpen && this.userInfoDropdown) {
      if (!this.userInfoDropdown.nativeElement.contains(target) && !target.closest('.user-info')) {
        this.isUserInfoOpen = false;
      }
    }
  }

  logout() {
    // Call the AuthService logout method to clear authentication state
    this.authService.logout();

    // Close the user info dropdown
    this.isUserInfoOpen = false;

    // Close any other open dropdowns for security
    this.isDropdownOpen = false;

    // Navigate to the welcome page
    this.router.navigate(['/welcome-to-receeto']).then(() => {
      console.log('User successfully logged out and redirected to welcome page');
    });
  }
}