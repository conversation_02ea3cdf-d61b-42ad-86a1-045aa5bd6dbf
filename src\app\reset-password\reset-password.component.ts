import { Component } from '@angular/core';
import { CommonModule } from '@angular/common'; 
import { FormsModule } from '@angular/forms';


@Component({
  selector: 'app-reset-password',
  standalone: true,
  imports: [CommonModule , FormsModule], 
  templateUrl: './reset-password.component.html',
  styleUrls: ['./reset-password.component.css']
})
export class ResetPasswordComponent {
  
  model = { newPassword: '', confirmPassword: '' };
  errorMessage = '';

  onSubmit() {
    if (this.model.newPassword && this.model.newPassword === this.model.confirmPassword) {
      // Simulate password reset
      console.log('Password reset successful');
    } else {
      this.errorMessage = 'Passwords do not match or are invalid.';
    }
  }
}