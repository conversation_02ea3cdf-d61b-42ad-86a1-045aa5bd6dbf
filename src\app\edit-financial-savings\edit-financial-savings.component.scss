// Variables
$primary-color: #6b48ff;
$secondary-color: #f8f9fa;
$text-primary: #333;
$text-secondary: #666;
$transition: all 0.3s ease;

.edit-savings-container {
  padding: 20px;
  margin-left: 270px;
  margin-top: 70px;
  transition: margin-left 0.3s;
  min-height: calc(100vh - 90px);
  background-color: var(--bg-color, #f8f9fa);
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;

  @media (max-width: 768px) {
    margin-left: 0;
    padding: 15px;
  }

  &.dark-mode {
    background-color: #1a1a1a;
    color: #fff;

    .edit-savings-header h1 {
      color: #fff;
    }

    .savings-form {
      background-color: #2a2a2a;
      border: 1px solid rgba(171, 85, 247, 0.15);
      box-shadow:
        0 10px 30px rgba(0, 0, 0, 0.3),
        0 6px 15px rgba(107, 72, 255, 0.1),
        0 0 0 1px rgba(171, 85, 247, 0.05),
        inset 0 0 0 1px rgba(255, 255, 255, 0.05);

      &::before {
        background: linear-gradient(135deg, rgba(107, 72, 255, 0.2), rgba(168, 85, 247, 0.2), rgba(183, 148, 244, 0.2));
        opacity: 0.3;
      }

      h2 {
        color: #fff;
      }

      .form-hint {
        color: #aaa;
      }

      input[type="text"],
      input[type="number"],
      input[type="date"] {
        background-color: #333;
        color: #fff;
        border-color: #444;

        &:focus {
          border-color: #6b48ff;
          box-shadow: 0 0 0 2px rgba(107, 72, 255, 0.2);
        }
      }

      .amount-option, .frequency-option, .period-option {
        background-color: #333;
        color: #ddd;

        &.selected {
          background-color: #6c5ce7;
          color: #fff;
          box-shadow: 0 4px 10px rgba(0, 0, 0, 0.3);
        }

        &:hover:not(.selected) {
          background-color: #444;
        }
      }

      .custom-amount-input .currency {
        color: #aaa;
      }

      .interest-label {
        color: #aaa;
      }

      .interest-value {
        color: #fff;
      }

      .form-actions {
        .cancel-btn {
          background-color: #444;
          color: #ddd;

          &:hover {
            background-color: #555;
          }
        }

        .save-btn {
          background: linear-gradient(135deg, #6b48ff, #a855f7);
        }
      }
    }
  }
}

.edit-savings-header {
  margin-bottom: 30px;
  text-align: center;
  width: 100%;

  h1 {
    font-size: 28px;
    font-weight: 600;
    margin: 0;
    color: #333;
    background: linear-gradient(135deg, #6b48ff, #a855f7);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    color: transparent;
  }
}

.savings-form {
  background-color: white;
  border-radius: 16px;
  width: 100%;
  max-width: 800px;
  overflow: hidden;
  transition: all 0.3s ease;
  position: relative;
  border: 1px solid rgba(171, 85, 247, 0.2);
  box-shadow:
    0 10px 30px rgba(107, 72, 255, 0.15),
    0 6px 15px rgba(168, 85, 247, 0.1),
    0 0 0 1px rgba(171, 85, 247, 0.05),
    inset 0 0 0 1px rgba(255, 255, 255, 0.3);
  padding: 30px;
  margin: 0 auto;

  &::before {
    content: '';
    position: absolute;
    top: -2px;
    left: -2px;
    right: -2px;
    bottom: -2px;
    background: linear-gradient(135deg, rgba(107, 72, 255, 0.4), rgba(168, 85, 247, 0.4), rgba(183, 148, 244, 0.4));
    border-radius: 18px;
    z-index: -1;
    opacity: 0.5;
    filter: blur(8px);
  }
}

.form-section {
  margin-bottom: 35px;
  position: relative;
  
  /* Fix for the unwanted hover effect on the saving-duration-section */
  &.saving-duration-section.dark-mode {
    pointer-events: inherit;
    
    &:hover {
      background-color: #2a2a2a !important;
      border-color: rgba(107, 72, 255, 0.2) !important;
      transform: none !important;
      box-shadow: none !important;
    }
  }

  &.dark-mode {
    background-color: #2a2a2a;
    border-radius: 12px;
    padding: 20px;
    border: 1px solid rgba(107, 72, 255, 0.2);

    // Prevent hover effect
    &:hover {
      background-color: #2a2a2a;
      border-color: rgba(107, 72, 255, 0.2);
    }

    h2 {
      color: #fff;
    }

    .form-control {
      background-color: #333;
      color: #fff;
      border-color: #444;

      &:focus {
        border-color: #6b48ff;
        box-shadow: 0 0 0 2px rgba(107, 72, 255, 0.2);
      }
    }

    .form-hint {
      color: #aaa;
    }
  }

  h2 {
    font-size: 20px;
    font-weight: 600;
    margin-bottom: 20px;
    color: #333;
    position: relative;
    display: inline-block;

    &::after {
      content: '';
      position: absolute;
      bottom: -5px;
      left: 0;
      width: 100%;
      height: 2px;
      background: linear-gradient(to right, #6b48ff, #a855f7);
      border-radius: 2px;
    }
  }
}

.form-group {
  margin-bottom: 20px;

  .form-control {
    width: 100%;
    padding: 14px;
    border: 1px solid #ddd;
    border-radius: 10px;
    font-size: 16px;
    transition: all 0.3s ease;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);

    &:focus {
      border-color: #6b48ff;
      outline: none;
      box-shadow: 0 0 0 3px rgba(107, 72, 255, 0.1);
    }
  }

  .form-hint {
    font-size: 13px;
    color: #888;
    margin-top: 8px;
    font-style: italic;
  }

  .invalid-input {
    border-color: #ff4444 !important;
    box-shadow: 0 0 0 1px #ff4444 !important;

    &:focus {
      border-color: #ff4444 !important;
      box-shadow: 0 0 0 2px rgba(255, 68, 68, 0.25) !important;
    }
  }
}

.amount-options, .frequency-options, .period-options {
  display: flex;
  flex-wrap: wrap;
  gap: 12px;
  margin-bottom: 20px;
}

.amount-option, .frequency-option, .period-option {
  background-color: #f5f5f5;
  border-radius: 30px;
  padding: 12px 24px;
  font-size: 15px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
  border: 1px solid transparent;
  position: relative;
  overflow: hidden;

  &:hover {
    background-color: #eaeaea;
    transform: translateY(-3px);
    box-shadow: 0 5px 10px rgba(0, 0, 0, 0.08);
  }

  &.selected {
    background: linear-gradient(135deg, #6b48ff, #a855f7);
    color: white;
    border-color: transparent;
    box-shadow: 0 5px 15px rgba(107, 72, 255, 0.3);
    transform: translateY(-3px) scale(1.05);
    z-index: 1;

    &::after {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: linear-gradient(
        90deg,
        rgba(255, 255, 255, 0) 0%,
        rgba(255, 255, 255, 0.1) 50%,
        rgba(255, 255, 255, 0) 100%
      );
      animation: shimmer 1.5s infinite;
    }
  }
}

// Styles for duration and savings section
.duration-inputs-container {
  display: flex;
  flex-wrap: wrap;
  gap: 20px;
  margin-bottom: 25px;
  
  .duration-amount-input,
  .duration-period-input {
    flex: 1;
    min-width: 250px;
  }

  .amount-input-group,
  .duration-input-group {
    display: flex;
    align-items: center;
    position: relative;
    margin-bottom: 8px;
    background-color: #333;
    border-radius: 12px;
    padding: 0;
    transition: all 0.3s ease;
    overflow: hidden;
    box-shadow: 0 0 0 1px rgba(171, 85, 247, 0.2);

    &:hover {
      box-shadow: 0 0 0 1px rgba(171, 85, 247, 0.3);
    }

    &:focus-within {
      box-shadow: 0 0 0 1px #6b48ff, 0 0 0 3px rgba(107, 72, 255, 0.1);
    }

    input {
      flex: 1;
      min-width: 100px;
      border: none !important;
      background: transparent;
      color: #fff;
      padding: 12px;
      box-shadow: none !important;
      outline: none !important;

      &:focus {
        outline: none !important;
        box-shadow: none !important;
        border: none !important;
      }
    }

    .duration-select {
      width: 120px;
      min-width: auto;
      border: none !important;
      background: transparent;
      color: #fff;
      padding: 12px;
      cursor: pointer;
      outline: none !important;
      box-shadow: none !important;
      -webkit-appearance: menulist;
      appearance: menulist;

      &:focus {
        outline: none !important;
        box-shadow: none !important;
        border: none !important;
      }
    }

    .currency {
      padding: 12px;
      color: #888;
      font-weight: 500;
      background-color: transparent;
    }
  }
}

.duration-total-calculation {
  background: rgba(107, 72, 255, 0.05);
  border-radius: 12px;
  padding: 20px;
  margin-top: 20px;
  border: 1px solid rgba(171, 85, 247, 0.15);

  .calculation-detail {
    margin-bottom: 15px;
    color: #bbb;
    font-size: 15px;
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    gap: 8px;

    .calculation-item {
      background: rgba(107, 72, 255, 0.1);
      padding: 4px 8px;
      border-radius: 4px;
      color: #fff;
      font-weight: 500;
      border: 1px solid rgba(171, 85, 247, 0.2);
    }

    .calculation-label {
      color: #888;
    }
  }

  .total-amount {
    font-size: 20px;
    font-weight: 600;
    color: #fff;
    background: linear-gradient(135deg, #6b48ff, #a855f7);
    -webkit-background-clip: text;
    background-clip: text;
    -webkit-text-fill-color: transparent;
    margin-top: 10px;
    padding-top: 10px;
    border-top: 1px solid rgba(171, 85, 247, 0.2);
  }
}

/* ============= DIRECT UI FIXES ============= */

/* Fix for saving-duration-section hover issue */
.form-section.saving-duration-section.dark-mode {
  transition: none !important;
  &:hover {
    background-color: #2a2a2a !important;
    border-color: rgba(107, 72, 255, 0.2) !important;
    transform: none !important;
    box-shadow: none !important;
  }
}

/* Fix for double border in input groups */
.amount-input-group, 
.duration-input-group {
  position: relative;
  display: flex;
  border: 1px solid rgba(171, 85, 247, 0.2) !important;
  border-radius: 12px !important;
  overflow: hidden !important;
  background-color: #333 !important;
  
  input.form-control {
    flex: 1 !important;
    border: none !important;
    background-color: transparent !important;
    box-shadow: none !important;
    outline: none !important;
  }
  
  select.duration-select,
  span.currency {
    border: none !important;
    border-left: none !important;
    background-color: transparent !important;
    box-shadow: none !important;
    outline: none !important;
  }
}

/* Fix for dropdown options in dark mode */
.form-section.dark-mode .duration-select option {
  background-color: #444 !important;
  color: white !important;
  font-weight: normal !important;
  padding: 10px !important;
}

.duration-savings-container {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
  align-items: flex-start;
  margin-bottom: 20px;
}

.duration-input-container,
.additional-savings-container {
  flex: 1;
  min-width: 200px;
  margin-bottom: 15px;
}

.duration-input-group,
.amount-input-group {
  display: flex;
  align-items: center;
  width: 100%;
  position: relative;
}

.duration-input {
  width: 60%;
  min-width: 100px;
  border-radius: 10px 0 0 10px !important;
  border-right: none !important;
}

.duration-unit {
  width: 40%;
  min-width: 100px;
  border-radius: 0 10px 10px 0 !important;
  background-color: #f5f5f5;
  border-color: #ddd;

  &:focus {
    border-color: #6b48ff;
    box-shadow: 0 0 0 3px rgba(107, 72, 255, 0.1);
  }
}

.form-label {
  display: block;
  margin-bottom: 8px;
  font-weight: 500;
  color: $text-secondary;
}

.total-calculation {
  margin-top: 25px;
  padding: 20px;
  background: linear-gradient(135deg, rgba(107, 72, 255, 0.05), rgba(168, 85, 247, 0.05));
  border-radius: 12px;
  border-left: 4px solid #6b48ff;

  .total-title {
    margin-bottom: 8px;
    font-weight: 600;
    font-size: 18px;
    color: $text-primary;
  }

  .calculation-detail {
    margin-bottom: 8px;
    font-size: 14px;
    color: $text-secondary;
  }

  .calculation-total {
    font-size: 1.2rem;
    font-weight: bold;
    color: $text-primary;
    background: linear-gradient(135deg, #6b48ff, #a855f7);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
  }
}

// Dark mode adjustments
.form-section.saving-duration-section.dark-mode {
  background-color: #2a2a2a;
  border-radius: 16px;
  padding: 25px;
  border: 1px solid rgba(171, 85, 247, 0.2);
  transition: all 0.3s ease;

  &:hover {
    box-shadow: none;
    transform: none;
  }

  box-shadow:
    0 10px 30px rgba(0, 0, 0, 0.3),
    0 6px 15px rgba(107, 72, 255, 0.15),
    0 0 0 1px rgba(171, 85, 247, 0.1),
    inset 0 0 0 1px rgba(255, 255, 255, 0.05);
  position: relative;
  overflow: hidden;
  transition: all 0.3s ease;

  &::before {
    content: '';
    position: absolute;
    top: -2px;
    left: -2px;
    right: -2px;
    bottom: -2px;
    background: linear-gradient(135deg, rgba(107, 72, 255, 0.2), rgba(168, 85, 247, 0.2), rgba(183, 148, 244, 0.2));
    border-radius: 18px;
    z-index: -1;
    opacity: 0.3;
    filter: blur(8px);
  }

  &:hover {
    box-shadow:
      0 15px 40px rgba(0, 0, 0, 0.4),
      0 8px 20px rgba(107, 72, 255, 0.2),
      0 0 0 1px rgba(171, 85, 247, 0.15),
      inset 0 0 0 1px rgba(255, 255, 255, 0.1);
    transform: translateY(-2px);
  }

  h2 {
    color: #fff;
    font-size: 22px;
    margin-bottom: 25px;

    &::after {
      background: linear-gradient(to right, #6b48ff, #a855f7);
      height: 3px;
      bottom: -8px;
    }
  }

  .form-label {
    color: #ddd;
    font-size: 16px;
    font-weight: 500;
    margin-bottom: 12px;
  }

  .duration-savings-container {
    background-color: rgba(107, 72, 255, 0.05);
    border-radius: 12px;
    padding: 20px;
    margin-bottom: 25px;
    border: 1px solid rgba(171, 85, 247, 0.15);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);

    .duration-input-container,
    .additional-savings-container {
      position: relative;

      &::after {
        content: '';
        position: absolute;
        bottom: -10px;
        left: 0;
        width: 100%;
        height: 1px;
        background: linear-gradient(to right, rgba(107, 72, 255, 0.2), rgba(168, 85, 247, 0.2), rgba(107, 72, 255, 0));
      }
    }
  }

  .duration-input-group,
  .amount-input-group {
    background-color: #333;
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.2);
    border: 1px solid rgba(171, 85, 247, 0.2);
    transition: all 0.3s ease;

    &:hover {
      box-shadow: 0 6px 15px rgba(0, 0, 0, 0.25);
      border-color: rgba(171, 85, 247, 0.3);
    }

    &:focus-within {
      box-shadow: 0 6px 15px rgba(107, 72, 255, 0.2);
      border-color: #6b48ff;
    }
  }

  .form-control {
    background-color: #333;
    color: #fff;
    border-color: transparent;
    padding: 16px;
    font-size: 16px;

    &:focus {
      border-color: transparent;
      box-shadow: none;
      background-color: #3a3a3a;
    }
  }

  .duration-input {
    border-right: 1px solid rgba(171, 85, 247, 0.2) !important;
  }

  .duration-unit {
    background-color: #333;
    color: #fff;
    border-color: transparent;
    padding-right: 40px;
    font-weight: 500;

    &:focus {
      border-color: transparent;
      box-shadow: none;
      background-color: #3a3a3a;
    }

    option {
      background-color: #333;
      color: #fff;
      padding: 15px;
    }
  }

  .form-hint {
    color: #aaa;
    margin-top: 10px;
    font-style: italic;
    font-size: 14px;
  }

  .currency {
    color: #a855f7;
    font-weight: 600;
    right: 20px;
  }

  .total-calculation {
    background: linear-gradient(135deg, rgba(107, 72, 255, 0.15), rgba(168, 85, 247, 0.15));
    border-radius: 12px;
    padding: 25px;
    margin-top: 30px;
    border: 1px solid rgba(171, 85, 247, 0.2);
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.25);
    position: relative;
    overflow: hidden;

    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      width: 4px;
      height: 100%;
      background: linear-gradient(to bottom, #6b48ff, #a855f7);
      border-radius: 4px 0 0 4px;
    }

    .total-title {
      color: #fff;
      font-size: 20px;
      font-weight: 600;
      margin-bottom: 15px;
      position: relative;
      display: inline-block;

      &::after {
        content: '';
        position: absolute;
        bottom: -5px;
        left: 0;
        width: 100%;
        height: 2px;
        background: linear-gradient(to right, #6b48ff, #a855f7);
        border-radius: 2px;
      }
    }

    .calculation-detail {
      color: #bbb;
      font-size: 15px;
      padding: 10px 0;
      border-bottom: 1px solid rgba(171, 85, 247, 0.2);
      margin-bottom: 15px;
      line-height: 1.6;

      .calculation-item {
        color: #fff;
        font-weight: 500;
        background-color: rgba(107, 72, 255, 0.1);
        padding: 3px 8px;
        border-radius: 4px;
        margin: 0 2px;
        display: inline-block;
        border: 1px solid rgba(171, 85, 247, 0.2);
      }

      .calculation-label {
        color: #aaa;
        font-size: 13px;
        font-style: italic;
      }
    }

    .calculation-total {
      font-size: 24px;
      font-weight: 700;
      background: linear-gradient(135deg, #6b48ff, #a855f7);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      background-clip: text;
      color: transparent;
      display: inline-block;
      padding: 5px 0;
    }
  }
}

.custom-amount-input, .custom-date-input {
  margin-top: 20px;
  position: relative;

  .form-control {
    width: 100%;
    padding: 14px;
    border: 1px solid #ddd;
    border-radius: 10px;
    font-size: 16px;
    transition: all 0.3s ease;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);

    &:focus {
      border-color: #6b48ff;
      outline: none;
      box-shadow: 0 0 0 3px rgba(107, 72, 255, 0.1);
    }
  }

  .currency {
    position: absolute;
    right: 15px;
    top: 50%;
    transform: translateY(-50%);
    color: #888;
    font-weight: 500;
    font-size: 15px;
  }
}

.interest-rate {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 20px;
  padding: 15px;
  background-color: #f8f9fa;
  border-radius: 10px;
  border-left: 4px solid #6b48ff;

  .interest-label {
    font-size: 15px;
    color: #666;
    font-weight: 500;
  }

  .interest-value {
    font-size: 18px;
    font-weight: 700;
    color: #333;
    background: linear-gradient(135deg, #6b48ff, #a855f7);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    color: transparent;
  }
}

.form-actions {
  display: flex;
  justify-content: space-between;
  margin-top: 40px;

  button {
    padding: 14px 30px;
    border-radius: 30px;
    font-size: 16px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
  }

  .cancel-btn {
    background-color: #f0f0f0;
    color: #333;
    border: none;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);

    &:hover {
      background-color: #e0e0e0;
      transform: translateY(-2px);
      box-shadow: 0 4px 10px rgba(0, 0, 0, 0.15);
    }

    &:active {
      transform: translateY(1px);
    }
  }

  .save-btn {
    background: linear-gradient(135deg, #6b48ff, #a855f7);
    color: white;
    border: none;
    box-shadow: 0 4px 15px rgba(107, 72, 255, 0.3);

    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: -100%;
      width: 100%;
      height: 100%;
      background: linear-gradient(
        90deg,
        rgba(255, 255, 255, 0) 0%,
        rgba(255, 255, 255, 0.2) 50%,
        rgba(255, 255, 255, 0) 100%
      );
      transition: all 0.5s ease;
    }

    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 6px 20px rgba(107, 72, 255, 0.4);

      &::before {
        left: 100%;
      }
    }

    &:active {
      transform: translateY(1px);
      box-shadow: 0 2px 10px rgba(107, 72, 255, 0.3);
    }
  }

  .delete-btn {
    background-color: #dc3545;
    color: white;
    border: none;
    padding: 14px 30px;
    border-radius: 30px;
    font-size: 16px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    margin: 0 10px;

    &:hover {
      background-color: #c82333;
      transform: translateY(-2px);
      box-shadow: 0 4px 10px rgba(220, 53, 69, 0.3);
    }

    &:active {
      transform: translateY(1px);
    }
  }
}

.loading-state, .not-found-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 400px;
  width: 100%;
  max-width: 600px;
  margin: 0 auto;

  .spinner {
    width: 50px;
    height: 50px;
    border: 4px solid rgba(107, 72, 255, 0.1);
    border-radius: 50%;
    border-top-color: #6b48ff;
    border-left-color: #a855f7;
    animation: spin 1.2s cubic-bezier(0.5, 0, 0.5, 1) infinite;
    box-shadow: 0 4px 10px rgba(107, 72, 255, 0.2);
  }

  @keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
  }

  p {
    margin-top: 20px;
    color: #666;
    font-size: 16px;
    font-weight: 500;
  }
}

.not-found-state {
  .message-content {
    text-align: center;
    background-color: white;
    border-radius: 16px;
    padding: 40px;
    box-shadow:
      0 10px 30px rgba(107, 72, 255, 0.15),
      0 6px 15px rgba(168, 85, 247, 0.1);
    width: 100%;
    max-width: 500px;
    border: 1px solid rgba(171, 85, 247, 0.1);

    i {
      font-size: 60px;
      color: #ff6b6b;
      margin-bottom: 20px;
      opacity: 0.9;
      animation: pulse 2s ease-in-out infinite;
    }

    @keyframes pulse {
      0% { transform: scale(1); }
      50% { transform: scale(1.1); }
      100% { transform: scale(1); }
    }

    h3 {
      font-size: 22px;
      font-weight: 600;
      margin: 0 0 15px;
      color: #333;
    }

    p {
      color: #666;
      margin-bottom: 25px;
      font-size: 16px;
      line-height: 1.5;
    }

    .back-btn {
      background: linear-gradient(135deg, #6b48ff, #a855f7);
      color: white;
      border: none;
      padding: 12px 25px;
      border-radius: 30px;
      font-size: 15px;
      font-weight: 600;
      cursor: pointer;
      transition: all 0.3s ease;
      box-shadow: 0 4px 15px rgba(107, 72, 255, 0.3);

      &:hover {
        transform: translateY(-2px);
        box-shadow: 0 6px 20px rgba(107, 72, 255, 0.4);
      }

      &:active {
        transform: translateY(1px);
      }
    }
  }
}

.validation-error {
  color: #ff4444;
  font-size: 13px;
  margin-top: 5px;
  font-weight: 500;
}

// Specific section styles
.saving-name-section {
  border-left: 4px solid #6b48ff;
}

.saving-amount-section {
  border-left: 4px solid #a855f7;
}

.saving-frequency-section {
  border-left: 4px solid #8b5cf6;
}

.saving-period-section {
  border-left: 4px solid #7c3aed;
}

.saving-duration-section {
  border-left: 4px solid #7b2cbf;
  padding: 20px;
  background-color: rgba(107, 72, 255, 0.03);
  border-radius: 12px;
  margin-bottom: 25px;

  .duration-input {
    .duration-unit {
      width: 40%;
      min-width: 100px;
      padding: 14px;
      border: 1px solid #ddd;
      border-radius: 10px;
      font-size: 16px;
      font-weight: 500;
      color: #333;
      background-color: #f5f5f5;
      cursor: pointer;
      appearance: none;
      -webkit-appearance: none;
      -moz-appearance: none;
      background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%23333' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
      background-repeat: no-repeat;
      background-position: right 12px center;
      background-size: 14px;
      padding-right: 35px;
      transition: all 0.3s ease;

      &:focus {
        border-color: #6b48ff;
        outline: none;
        box-shadow: 0 0 0 3px rgba(107, 72, 255, 0.1);
      }

      &:hover {
        border-color: #6b48ff;
      }

      option {
        padding: 10px;
        font-size: 16px;
        background-color: #fff;
        color: #333;
      }
    }
  }

  &.dark-mode {
    background-color: #2a2a2a;
    border: 1px solid rgba(107, 72, 255, 0.2);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);

    .duration-input {
      .duration-unit {
        background-color: #333;
        color: #fff;
        border-color: #444;
        background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%23fff' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");

        &:focus {
          border-color: #6b48ff;
          box-shadow: 0 0 0 3px rgba(107, 72, 255, 0.2);
        }

        &:hover {
          border-color: #555;
        }

        option {
          background-color: #333;
          color: #fff;
          padding: 12px;
        }
      }
    }
  }
}

// Dark mode adjustments
.dark-mode {
  .validation-error {
    color: #ff6b6b;
  }

  .form-group {
    .invalid-input {
      border-color: #ff6b6b !important;
      box-shadow: 0 0 0 1px #ff6b6b !important;

      &:focus {
        border-color: #ff6b6b !important;
        box-shadow: 0 0 0 2px rgba(255, 107, 107, 0.25) !important;
      }
    }
  }

  .saving-duration-section {
    background-color: #2a2a2a;
    border: 1px solid rgba(107, 72, 255, 0.2);

    .duration-input {
      .form-control, .duration-unit {
        background-color: #333;
        color: #fff;
        border-color: #444;

        &:focus {
          border-color: #6b48ff;
          box-shadow: 0 0 0 3px rgba(107, 72, 255, 0.2);
        }
      }
    }

    .additional-savings {
      label {
        color: #aaa;
      }

      .amount-input {
        .form-control {
          background-color: #333;
          color: #fff;
          border-color: #444;

          &:focus {
            border-color: #6b48ff;
            box-shadow: 0 0 0 3px rgba(107, 72, 255, 0.2);
          }
        }

        .currency {
          color: #aaa;
        }
      }
    }

    .total-calculation {
      background-color: #333;
      border-color: #444;

      .calculation-label {
        color: #aaa;
      }

      .calculation-detail {
        color: #aaa;
        border-bottom-color: rgba(107, 72, 255, 0.3);
      }

      .calculation-total {
        color: #fff;
        background: linear-gradient(135deg, #6b48ff, #a855f7);
        -webkit-background-clip: text;
        background-clip: text;
        color: transparent;
      }
    }
  }
}
