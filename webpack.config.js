const path = require('path');
const CompressionPlugin = require('compression-webpack-plugin');
const TerserPlugin = require('terser-webpack-plugin');
const BundleAnalyzerPlugin = require('webpack-bundle-analyzer').BundleAnalyzerPlugin;

module.exports = {
  // Use production mode for optimizations
  mode: 'production',
  
  // Optimization configurations
  optimization: {
    minimizer: [
      new TerserPlugin({
        terserOptions: {
          compress: {
            drop_console: true, // Remove console logs in production
            pure_funcs: ['console.log'] // Remove console.log specifically
          },
          output: {
            comments: false // Remove comments
          },
          mangle: true // Name mangling for smaller file sizes
        },
        extractComments: false, // Don't extract comments to separate file
        parallel: true // Use multi-process parallel running for improved build speed
      }),
    ],
    runtimeChunk: 'single', // Extract runtime code into a separate chunk
    splitChunks: {
      chunks: 'all', // Split all chunks, not just async ones
      maxInitialRequests: Infinity, // No limit on initial requests for splitting
      minSize: 20000, // Only split bundles larger than 20kb
      cacheGroups: {
        vendor: {
          test: /[\\/]node_modules[\\/]/, 
          name(module) {
            // Get the name. E.g. node_modules/packageName/not/this/part.js or node_modules/packageName
            const packageName = module.context.match(/[\\/]node_modules[\\/]([^@\\/]+)/);
            if (!packageName) return 'vendor';
            // npm package names are URL-safe, but some servers don't like @ symbols
            return `npm.${packageName[1].replace('@', '')}`;
          },
          priority: -10
        },
        styles: {
          name: 'styles',
          test: /\.css$/,
          chunks: 'all',
          enforce: true
        }
      }
    }
  },
  
  // Plugins for additional optimizations
  plugins: [
    // Gzip compression for assets
    new CompressionPlugin({
      algorithm: 'gzip',
      test: /\.(js|css|html|svg)$/,
      threshold: 10240, // Only compress assets bigger than 10kb
      minRatio: 0.8 // Only compress assets that compress well (80% or better)
    }),
    
    // Bundle analyzer for visualization (disabled by default, enable when needed)
    // new BundleAnalyzerPlugin()
  ],
  
  // Performance hints configuration
  performance: {
    hints: 'warning', // 'error' or false are other options
    maxAssetSize: 512000, // Size in bytes
    maxEntrypointSize: 512000, // Size in bytes
  },
};