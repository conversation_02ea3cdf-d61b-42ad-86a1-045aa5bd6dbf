/* Dashboard Component SCSS - Modern Design */

// Variables
$primary-color: #6b48ff;
$secondary-color: #e8f0fe;
$success-color: #34c759;
$danger-color: #ff4d4f;
$warning-color: #ffcc00;
$text-primary: #333333;
$text-secondary: #666666;
$text-light: #999999;
$border-color: #e8e8e8;
$card-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
$transition: all 0.3s ease;

// Mixins
@mixin flex($direction: row, $justify: flex-start, $align: flex-start, $wrap: nowrap) {
  display: flex;
  flex-direction: $direction;
  justify-content: $justify;
  align-items: $align;
  flex-wrap: $wrap;
}

@mixin card {
  background: white;
  border-radius: 12px;
  box-shadow: $card-shadow;
  padding: 20px;
  transition: $transition;
}

* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

/* General styles for the dashboard */
.dashboard-container {
  font-family: 'Inter', 'Segoe UI', Arial, sans-serif;
  display: flex;
  flex-direction: column;
  padding: 20px;
  margin-top: 80px; /* Added space between top navbar and dashboard content */
  transition: $transition;
  color: $text-primary;
}

.main-content {
  width: 100%;
  max-width: 1400px;
  margin: 0 auto;
}

// Header styles
.header {
  @include flex(row, space-between, center);
  margin-bottom: 24px;
  
  .header-left {
    @include flex(column, center, flex-start);
    
    .breadcrumb {
      font-size: 14px;
      color: $text-light;
      margin-bottom: 8px;
    }
    
    h1 {
      font-size: 24px;
      font-weight: 600;
      color: $text-primary;
    }
  }
  
  .header-right {
    @include flex(row, flex-end, center);
    gap: 20px;
    
    .date-picker {
      @include flex(row, center, center);
      gap: 8px;
      background: white;
      padding: 8px 12px;
      border-radius: 8px;
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
      
      .date {
        font-size: 14px;
        color: $text-secondary;
      }
      
      i {
        color: $primary-color;
        font-size: 16px;
      }
    }
    
    .header-actions {
      @include flex(row, center, center);
      gap: 12px;
      
      .icon-btn {
        width: 40px;
        height: 40px;
        border-radius: 50%;
        background: white;
        border: none;
        @include flex(row, center, center);
        cursor: pointer;
        transition: $transition;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
        
        i {
          color: $text-secondary;
          font-size: 16px;
        }
        
        &:hover {
          background: $secondary-color;
          
          i {
            color: $primary-color;
          }
        }
      }
      
      .user-profile {
        width: 40px;
        height: 40px;
        border-radius: 50%;
        overflow: hidden;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        
        .profile-img {
          width: 100%;
          height: 100%;
          object-fit: cover;
        }
      }
    }
  }
}

// Location cards
.locations {
  @include flex(row, flex-start, stretch, wrap);
  gap: 16px;
  margin-bottom: 24px;
  padding-top: 10px; /* Additional padding for locations section */
  
  .location-card {
    @include card;
    flex: 1;
    min-width: 200px;
    @include flex(row, flex-start, center);
    gap: 16px;
    padding: 16px;
    border-left: 4px solid $primary-color;
    transition: transform 0.2s ease;
    
    &:hover {
      transform: translateY(-4px);
    }
    
    .location-icon {
      background: rgba($primary-color, 0.1);
      color: $primary-color;
      width: 48px;
      height: 48px;
      border-radius: 12px;
      @include flex(row, center, center);
      
      i {
        font-size: 20px;
      }
    }
    
    .location-details {
      flex: 1;
      
      .location-name {
        font-weight: 600;
        font-size: 16px;
        color: $text-primary;
        margin-bottom: 4px;
      }
      
      .location-tickets {
        font-size: 14px;
        color: $primary-color;
        font-weight: 500;
      }
    }
  }
  
  .add-location {
    @include flex(row, center, center);
    min-width: 180px;
    
    .add-btn {
      background: white;
      border: 1px dashed $border-color;
      padding: 12px 20px;
      border-radius: 12px;
      @include flex(row, center, center);
      gap: 8px;
      cursor: pointer;
      transition: $transition;
      width: 100%;
      height: 100%;
      color: $text-secondary;
      font-weight: 500;
      
      i {
        color: $primary-color;
        font-size: 16px;
      }
      
      &:hover {
        background: rgba($primary-color, 0.05);
        border-color: rgba($primary-color, 0.3);
        color: $primary-color;
      }
    }
  }
}

// Stats cards
.stats-cards {
  @include flex(row, space-between, stretch, wrap);
  gap: 16px;
  margin-bottom: 24px;
  
  .stat-card {
    @include card;
    flex: 1;
    min-width: 200px;
    padding: 24px;
    position: relative;
    overflow: hidden;
    text-align: left;
    
    &:hover {
      transform: translateY(-4px);
      box-shadow: 0 8px 16px rgba(0, 0, 0, 0.1);
    }
    
    .stat-indicator {
      position: absolute;
      top: 0;
      left: 0;
      height: 4px;
      width: 100%;
      
      &.purple {
        background: $primary-color;
      }
      
      &.blue {
        background: #4285f4;
      }
      
      &.red {
        background: $danger-color;
      }
      
      &.green {
        background: $success-color;
      }
    }
    
    h3 {
      font-size: 28px;
      font-weight: 700;
      color: $text-primary;
      margin: 16px 0 8px;
    }
    
    p {
      font-size: 14px;
      color: $text-secondary;
      margin: 0;
    }
    
    .more-options {
      position: absolute;
      top: 16px;
      right: 16px;
      background: transparent;
      border: none;
      color: $text-light;
      cursor: pointer;
      padding: 4px;
      border-radius: 4px;
      
      &:hover {
        background: rgba(0, 0, 0, 0.05);
        color: $text-primary;
      }
      
      i {
        font-size: 16px;
      }
    }
  }
}

// Charts section
.charts {
  @include flex(row, space-between, stretch, wrap);
  gap: 20px;
  margin-bottom: 24px;
  
  .chart {
    @include card;
    padding: 0;
    overflow: hidden;
    
    .chart-header {
      padding: 20px 20px 0;
      
      h3 {
        font-size: 16px;
        font-weight: 600;
        color: $text-light;
        margin: 0;
      }
      
      p {
        font-size: 18px;
        font-weight: 600;
        color: $text-primary;
        margin: 4px 0 0;
      }
    }
    
    &.avg-spend {
      flex: 2;
      min-width: 60%;
      
      .chart-header {
        @include flex(row, space-between, flex-start);
        margin-bottom: 20px;
        
        .chart-legend {
          @include flex(row, flex-end, center);
          flex-wrap: wrap;
          gap: 12px;
          
          .legend-item {
            @include flex(row, center, center);
            gap: 6px;
            font-size: 12px;
            color: $text-secondary;
            
            .legend-dot {
              width: 8px;
              height: 8px;
              border-radius: 50%;
              
              &.purple {
                background: $primary-color;
              }
              
              &.blue {
                background: #4285f4;
              }
              
              &.green {
                background: $success-color;
              }
              
              &.orange {
                background: #ff9800;
              }
            }
          }
          
          .period-selector {
            @include flex(row, center, center);
            gap: 6px;
            font-size: 12px;
            color: $text-primary;
            background: rgba($secondary-color, 0.5);
            padding: 6px 12px;
            border-radius: 16px;
            cursor: pointer;
            
            &:hover {
              background: $secondary-color;
            }
            
            i {
              font-size: 10px;
            }
          }
        }
      }
      
      .chart-content {
        padding: 0 20px 20px;
        
        .bar-chart {
          @include flex(row, flex-start, flex-end);
          height: 240px;
          
          .y-axis {
            @include flex(column, space-between, flex-end);
            height: 100%;
            padding-right: 10px;
            width: 50px;
            
            .y-label {
              font-size: 10px;
              color: $text-light;
            }
          }
          
          .chart-bars {
            @include flex(row, space-between, flex-end);
            flex: 1;
            height: 100%;
            
            .month-column {
              @include flex(column, flex-end, center);
              flex: 1;
              height: 100%;
              
              .bar-group {
                @include flex(column, flex-end, center);
                width: 100%;
                height: calc(100% - 20px);
                position: relative;
                
                .bar {
                  width: 70%;
                  margin: 0 auto;
                  border-radius: 4px 4px 0 0;
                  position: absolute;
                  bottom: 0;
                  
                  &.purple {
                    background: rgba($primary-color, 0.7);
                    left: 0;
                    width: 33%;
                  }
                  
                  &.blue {
                    background: rgba(#4285f4, 0.7);
                    left: 33%;
                    width: 33%;
                  }
                  
                  &.green {
                    background: rgba($success-color, 0.7);
                    left: 66%;
                    width: 33%;
                  }
                }
              }
              
              .x-label {
                font-size: 12px;
                color: $text-secondary;
                margin-top: 8px;
              }
            }
          }
        }
      }
    }
    
    &.monthly-income {
      flex: 1;
      min-width: 250px;
      @include flex(column, flex-start, center);
      padding-bottom: 20px;
      
      .income-circle {
        width: 180px;
        height: 180px;
        border-radius: 50%;
        border: 16px solid $primary-color;
        border-right-color: #e0e0e0;
        @include flex(row, center, center);
        margin: 20px 0;
        
        .circle-text {
          text-align: center;
          
          p {
            font-size: 14px;
            color: $text-light;
            margin: 0 0 4px;
          }
          
          h4 {
            font-size: 18px;
            font-weight: 700;
            color: $text-primary;
            margin: 0;
          }
        }
      }
      
      .income-legend {
        @include flex(column, center, flex-start);
        gap: 12px;
        width: 100%;
        padding: 0 20px;
        
        .legend-item {
          @include flex(row, space-between, center);
          width: 100%;
          font-size: 14px;
          color: $text-secondary;
          
          .legend-color {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
          }
          
          .turn-over {
            background: $primary-color;
          }
          
          .until-objective {
            background: #e0e0e0;
          }
          
          .percentage {
            font-weight: 600;
            color: $text-primary;
          }
        }
      }
    }
  }
}

// Table section
.table-section {
  @include card;
  margin-top: 0;
  padding: 0;
  overflow: hidden;
  
  .table-header {
    @include flex(row, space-between, center);
    padding: 16px 20px;
    border-bottom: 1px solid $border-color;
    
    .entries {
      @include flex(row, flex-start, center);
      gap: 8px;
      font-size: 14px;
      color: $text-secondary;
      
      select {
        padding: 6px 12px;
        border: 1px solid $border-color;
        border-radius: 8px;
        font-size: 14px;
        color: $text-primary;
        background-color: white;
        cursor: pointer;
        
        &:focus {
          outline: none;
          border-color: $primary-color;
        }
      }
    }
    
    .search-container {
      position: relative;
      
      i {
        position: absolute;
        left: 12px;
        top: 50%;
        transform: translateY(-50%);
        color: $text-light;
        font-size: 14px;
      }
      
      .search-input {
        padding: 8px 12px 8px 36px;
        border: 1px solid $border-color;
        border-radius: 8px;
        font-size: 14px;
        width: 240px;
        
        &:focus {
          outline: none;
          border-color: $primary-color;
        }
        
        &::placeholder {
          color: $text-light;
        }
      }
    }
    
    .table-options {
      width: 32px;
      height: 32px;
      border-radius: 8px;
      background: transparent;
      border: none;
      @include flex(row, center, center);
      cursor: pointer;
      
      &:hover {
        background: rgba(0, 0, 0, 0.05);
      }
      
      i {
        color: $text-secondary;
        font-size: 16px;
      }
    }
  }
  
  table {
    width: 100%;
    border-collapse: collapse;
    
    th {
      padding: 16px 20px;
      text-align: left;
      font-size: 14px;
      font-weight: 600;
      color: $text-primary;
      background-color: #fafafa;
      position: relative;
      
      i {
        font-size: 12px;
        color: $text-light;
        margin-left: 4px;
      }
      
      &:after {
        content: '';
        position: absolute;
        bottom: 0;
        left: 0;
        width: 100%;
        height: 1px;
        background-color: $border-color;
      }
    }
    
    td {
      padding: 16px 20px;
      text-align: left;
      font-size: 14px;
      color: $text-secondary;
      border-bottom: 1px solid $border-color;
      
      &.product-cell {
        @include flex(row, flex-start, center);
        gap: 12px;
        
        .product-img {
          width: 32px;
          height: 32px;
          border-radius: 8px;
          object-fit: cover;
        }
      }
      
      .status-loyal {
        display: inline-block;
        background: rgba($success-color, 0.1);
        color: $success-color;
        padding: 4px 12px;
        border-radius: 16px;
        font-size: 12px;
        font-weight: 500;
      }
      
      .status-new {
        display: inline-block;
        background: rgba(#4285f4, 0.1);
        color: #4285f4;
        padding: 4px 12px;
        border-radius: 16px;
        font-size: 12px;
        font-weight: 500;
      }
      
      &.action-cell {
        @include flex(row, flex-start, center);
        gap: 8px;
        
        .action-btn {
          width: 32px;
          height: 32px;
          border-radius: 8px;
          background: transparent;
          border: none;
          @include flex(row, center, center);
          cursor: pointer;
          
          i {
            font-size: 14px;
          }
          
          &.view {
            color: $text-secondary;
            
            &:hover {
              background: rgba($primary-color, 0.1);
              color: $primary-color;
            }
          }
          
          &.delete {
            color: $text-light;
            
            &:hover {
              background: rgba($danger-color, 0.1);
              color: $danger-color;
            }
          }
        }
      }
    }
    
    tr {
      &:hover {
        td {
          background-color: rgba($secondary-color, 0.3);
        }
      }
    }
  }
}

/* Responsive Breakpoints */
@media (max-width: 768px) {
  .dashboard-container {
    padding: 10px;
  }

  .user-avatar {
    display: block;
    position: absolute;
    top: 20px;
    right: 20px;
    width: 40px;
    height: 40px;
    cursor: pointer;
    z-index: 1001;

    img {
      width: 100%;
      height: 100%;
      border-radius: 50%;
    }
  }

  .header {
    flex-direction: column;
    align-items: flex-start;
    gap: 15px;

    h1 {
      font-size: 1.2rem;
    }

    .header-right {
      gap: 10px;
      justify-content: flex-end;
      width: 100%;

      i {
        font-size: 1rem;
      }
    }

    .header-avatar {
      width: 25px;
      height: 25px;
    }
  }

  .locations {
    flex-direction: column;
    gap: 15px;

    .location-card {
      padding: 10px;
    }

    .location-icon {
      width: 30px;
      height: 30px;
    }

    .location-details {
      font-size: 0.8rem;
    }

    .add-btn {
      padding: 8px 15px;
      font-size: 0.8rem;
    }
  }

  .stats-cards {
    flex-direction: column;
    gap: 15px;

    .stat-card {
      min-width: 100%; /* Ensure cards take full width on mobile */
      padding: 15px;

      h3 {
        font-size: 1.2rem;
      }

      p {
        font-size: 0.8rem;
      }
    }
  }

  .charts {
    flex-direction: column;
    gap: 15px;

    .avg-spend,
    .monthly-income {
      flex: none;
    }

    .avg-spend h3,
    .monthly-income h3 {
      font-size: 1reme;
    }

    .chart-placeholder {
      height: 150px;
    }

    .income-circle {
      width: 120px;
      height: 120px;
      border-width: 8px;

      .circle-text {
        p {
          font-size: 0.8rem;
        }

        h4 {
          font-size: 1rem;
        }
      }
    }

    .income-legend {
      gap: 10px;

      .legend-item {
        font-size: 0.8rem;
      }
    }
  }

  .table-section {
    padding: 10px;

    .table-header {
      flex-direction: column;
      gap: 10px;
    }

    .entries {
      font-size: 0.8rem;
    }

    .search-input {
      padding: 4px 8px;
      font-size: 0.8rem;
      width: 100%;
    }

    th,
    td {
      padding: 8px;
      font-size: 0.8rem;
    }
  }
}

@media (max-width: 480px) {
  .dashboard-container {
    padding: 8px;
  }

  .header {
    gap: 10px;

    h1 {
      font-size: 1rem;
    }

    .header-right {
      gap: 8px;

      i {
        font-size: 0.9rem;
      }
    }

    .header-avatar {
      width: 20px;
      height: 20px;
    }
  }

  .locations {
    gap: 10px;

    .location-card {
      padding: 8px;
    }

    .location-icon {
      width: 25px;
      height: 25px;
    }

    .location-details {
      font-size: 0.7rem;
    }

    .add-btn {
      padding: 6px 12px;
      font-size: 0.7rem;
    }
  }

  .stats-cards {
    gap: 10px;

    .stat-card {
      padding: 12px;

      h3 {
        font-size: 1rem;
      }

      p {
        font-size: 0.7rem;
      }
    }
  }

  .charts {
    gap: 10px;

    .avg-spend h3,
    .monthly-income h3 {
      font-size: 0.9rem;
    }

    .chart-placeholder {
      height: 120px;
    }

    .income-circle {
      width: 100px;
      height: 100px;
      border-width: 6px;

      .circle-text {
        p {
          font-size: 0.7rem;
        }

        h4 {
          font-size: 0.9rem;
        }
      }
    }

    .income-legend {
      gap: 8px;

      .legend-item {
        font-size: 0.7rem;
      }
    }
  }

  .table-section {
    padding: 8px;

    .table-header {
      gap: 8px;
    }

    .entries {
      font-size: 0.7rem;
    }

    .search-input {
      padding: 3px 6px;
      font-size: 0.7rem;
    }

    th,
    td {
      padding: 6px;
      font-size: 0.7rem;
    }
  }
}