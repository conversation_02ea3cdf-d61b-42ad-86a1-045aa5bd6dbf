{"$schema": "./node_modules/@angular/cli/lib/config/schema.json", "version": 1, "cli": {"analytics": false, "warnings": {"versionMismatch": false}}, "newProjectRoot": "projects", "projects": {"receetoProject": {"projectType": "application", "schematics": {"@schematics/angular:component": {"style": "scss"}}, "root": "", "sourceRoot": "src", "prefix": "app", "architect": {"build": {"builder": "@angular-devkit/build-angular:browser", "options": {"outputPath": "dist/receeto-project", "index": "src/index.html", "main": "src/main.ts", "polyfills": ["zone.js"], "tsConfig": "tsconfig.app.json", "assets": ["src/favicon.ico", "src/assets"], "styles": ["src/styles.css"], "scripts": [], "optimization": {"scripts": true, "styles": {"minify": true, "inlineCritical": true}, "fonts": true}, "vendorChunk": true, "extractLicenses": false, "buildOptimizer": true, "sourceMap": false, "namedChunks": true}, "configurations": {"production": {"budgets": [{"type": "initial", "maximumWarning": "2mb", "maximumError": "5mb"}, {"type": "anyComponentStyle", "maximumWarning": "10kb", "maximumError": "20kb"}], "fileReplacements": [{"replace": "src/environments/environment.ts", "with": "src/environments/environment.prod.ts"}], "outputHashing": "all", "sourceMap": false, "namedChunks": false, "aot": true, "extractLicenses": true, "vendorChunk": false, "buildOptimizer": true, "optimization": true}, "optimized": {"buildOptimizer": true, "optimization": true, "vendorChunk": true, "extractLicenses": false, "sourceMap": false, "namedChunks": true}, "development": {"buildOptimizer": false, "optimization": false, "vendorChunk": true, "extractLicenses": false, "sourceMap": true, "namedChunks": true}}}, "serve": {"builder": "@angular-devkit/build-angular:dev-server", "defaultConfiguration": "development", "options": {"buildTarget": "receetoProject:build", "hmr": true, "liveReload": true, "poll": 2000, "disableHostCheck": true}, "configurations": {"production": {"buildTarget": "receetoProject:build:production"}, "development": {"buildTarget": "receetoProject:build:development"}, "optimized": {"buildTarget": "receetoProject:build:optimized"}}}}}}}